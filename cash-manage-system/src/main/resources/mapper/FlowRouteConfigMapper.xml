<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.FlowRouteConfigMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.FlowRouteConfig">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="flowId" column="flow_id" jdbcType="VARCHAR"/>
        <result property="capitalId" column="capital_id" jdbcType="VARCHAR"/>
        <result property="priority" column="priority" jdbcType="INTEGER"/>
        <result property="enabled" column="enabled" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="valid" column="valid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,flow_id,capital_id,
        priority,enabled,remark,
        revision,created_by,created_time,
        updated_by,updated_time,valid
    </sql>

    <select id="query" resultType="com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigResponse">
        select a.id, a.priority, b.bank_channel as bankChannel, b.id as capitalId, b.enabled as capitalEnabled, a.enabled, a.valid
        from flow_route_config a
                 left join capital_config b on a.capital_id = b.id
        where a.flow_id = #{flowId}
          and a.valid = 'Y'
        order by a.priority
    </select>
    <select id="queryFlowRouteConfig"
            resultType="com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigResponse">
        select a.id,
               a.priority,
               b.enabled      as capitalEnabled,
               b.bank_channel as bankChannel,
               b.id           as capitalId,
               a.enabled,
               true           as selected
        from flow_route_config a
                 left join capital_config b on a.capital_id = b.id
        where a.flow_id = #{flowId}
          and a.valid = 'Y'
    </select>
    <select id="queryFlowRouteConfigPage"
            resultType="com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigPageResponse">
        SELECT a.id,
               a.flow_channel,
               a.created_time as createdTime,
               a.updated_by   as updatedBy,
               a.created_by   as createdBy,
               a.updated_time as updatedTime,
               a.enabled      as flowEnabled,
               group_concat(concat(b.priority, '.', c.bank_channel, '-', b.enabled) order by b.priority SEPARATOR
                            ';') as capitalConfigStr
        FROM flow_config a
                 LEFT JOIN flow_route_config b on a.id = b.flow_id
                 left join capital_config c on
            c.id = b.capital_id
        where b.valid = 'Y'
        group by a.id, a.enabled, a.created_time
        order by a.enabled desc, a.created_time DESC
    </select>


</mapper>
