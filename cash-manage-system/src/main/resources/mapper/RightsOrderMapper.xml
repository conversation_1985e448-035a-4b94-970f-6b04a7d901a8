<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.RightsOrderMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.RightsOrder">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <result property="loanId" column="loan_id" jdbcType="VARCHAR"/>
        <result property="sourceId" column="source_id" jdbcType="VARCHAR"/>
        <result property="outOrderNo" column="out_order_no" jdbcType="VARCHAR"/>
        <result property="payAmount" column="pay_amount" jdbcType="DECIMAL"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="state" column="state" jdbcType="VARCHAR"/>
        <result property="payChannel" column="pay_channel" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="realName" column="real_name" jdbcType="VARCHAR"/>
        <result property="packageId" column="package_id" jdbcType="VARCHAR"/>
        <result property="rightsSupplier" column="rights_supplier" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>


    <sql id="Base_Column_List">
        id
        ,user_id,order_id,
        loan_id,source_id,out_order_no,
        pay_amount,pay_time,state,
        pay_channel,phone,real_name,
        package_id,rights_supplier,remark,
        revision,created_by,created_time,
        updated_by,updated_time
    </sql>


    <select id="selectRightsOrder" parameterType="com.jinghang.cash.pojo.RightsOrder" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rights_order
        WHERE 1 = 1
        <if test="id!= null">
            AND id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="userId!= null">
            AND user_id = #{userId,jdbcType=VARCHAR}
        </if>
    </select>

    <resultMap id="RightsInfoVoMap" type="com.jinghang.cash.modules.manage.vo.res.RightsInfoQueryResponse">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <result property="loanId" column="loan_id" jdbcType="VARCHAR"/>
        <result property="mobile" column="phone" jdbcType="VARCHAR"/>
        <result property="repayState" column="state" jdbcType="VARCHAR"/>
        <result property="repayMode" column="pay_channel" jdbcType="VARCHAR"/>
        <result property="payNo" column="out_order_no" jdbcType="VARCHAR"/>
        <result property="rightsPackageId" column="id" jdbcType="VARCHAR"/>
        <result property="code" column="package_id" jdbcType="VARCHAR"/>
        <result property="rightsActualAmount" column="pay_amount" jdbcType="DECIMAL"/>
        <result property="loanTime" column="loan_time" jdbcType="TIMESTAMP"/>
        <result property="name" column="real_name" jdbcType="VARCHAR"/>
        <result property="rightsSupplier" column="rights_supplier" jdbcType="VARCHAR"/>
        <result property="planRepayDate" column="planRepayDate" jdbcType="TIMESTAMP"/>
        <result property="refundAmount" column="refund_amount" jdbcType="DECIMAL"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="refundTime" column="refund_time" jdbcType="TIMESTAMP"/>


    </resultMap>


    <select id="selectRightsInfoQueryRequest"
            parameterType="com.jinghang.cash.modules.manage.vo.req.RightsInfoQueryRequest" resultMap="RightsInfoVoMap">
        SELECT
        b.id,
        b.out_order_no,
        b.loan_id,
        b.real_name,
        b.phone,
        b.rights_supplier,
        b.package_id,
        b.pay_amount,
        b.pay_channel,
        b.state,
        a.loan_time,
        b.pay_time,
        b.order_id,
        DATE_ADD( a.loan_time, INTERVAL 2 DAY ) AS planRepayDate,
        c.refund_amount,
        c.refund_time
        FROM
        rights_order b
        LEFT JOIN loan a ON a.id = b.loan_id
        LEFT JOIN rights_refund_order c ON b.id = c.rights_order_id and c.refund_state = 'SUCCEED'
        WHERE 1= 1
        <if test="orderId!= null and orderId!= '' ">
            AND b.order_id = #{orderId,jdbcType=VARCHAR}
        </if>
        <if test="rightsSupplier!= null and rightsSupplier != ''">

            AND b.rights_supplier = #{rightsSupplier,jdbcType=VARCHAR}
        </if>
        <if test="payNo!= null and payNo !=''">
            AND b.out_order_no = #{payNo,jdbcType=VARCHAR}
        </if>
        <if test="mobile!= null and mobile != ''">
            AND b.phone = #{mobile,jdbcType=VARCHAR}
        </if>
        <if test="repayState!= null and repayState != ''">
            AND b.state = #{repayState,jdbcType=VARCHAR}
        </if>
        <if test="repayMode!= null and repayMode !='' ">
            AND b.pay_channel = #{repayMode,jdbcType=VARCHAR}
        </if>
        <if test="startDate!= null">
            AND b.created_time &gt;= #{startDate,jdbcType=TIMESTAMP}
        </if>
        <if test="endDate!= null">
            AND b.created_time &lt;= #{endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="refundStartDate!= null">
            AND c.refund_time &gt;= #{refundStartDate,jdbcType=TIMESTAMP}
        </if>
        <if test="refundEndDate!= null">
            AND c.refund_time &lt;= #{refundEndDate,jdbcType=TIMESTAMP}
        </if>
    </select>
    <select id="selectRightsAmountAndRefundAmount" parameterType="com.jinghang.cash.modules.manage.vo.req.RightsInfoQueryRequest" resultMap="RightsInfoVoMap">
        SELECT
        sum(b.pay_amount) as pay_amount,
        sum(c.refund_amount) as refund_amount
        FROM
        rights_order b
        LEFT JOIN rights_refund_order c ON b.id = c.rights_order_id and c.refund_state = 'SUCCEED'
        WHERE 1= 1
        <if test="orderId!= null and orderId!= '' ">
            AND b.order_id = #{orderId,jdbcType=VARCHAR}
        </if>
        <if test="rightsSupplier!= null and rightsSupplier != ''">

            AND b.rights_supplier = #{rightsSupplier,jdbcType=VARCHAR}
        </if>
        <if test="payNo!= null and payNo !=''">
            AND b.out_order_no = #{payNo,jdbcType=VARCHAR}
        </if>
        <if test="mobile!= null and mobile != ''">
            AND b.phone = #{mobile,jdbcType=VARCHAR}
        </if>
        <if test="repayState!= null and repayState != ''">
            AND b.state = #{repayState,jdbcType=VARCHAR}
        </if>
        <if test="repayMode!= null and repayMode !='' ">
            AND b.pay_channel = #{repayMode,jdbcType=VARCHAR}
        </if>
        <if test="startDate!= null">
            AND b.created_time &gt;= #{startDate,jdbcType=TIMESTAMP}
        </if>
        <if test="endDate!= null">
            AND b.created_time &lt;= #{endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="refundStartDate!= null">
            AND c.refund_time &gt;= #{refundStartDate,jdbcType=TIMESTAMP}
        </if>
        <if test="refundEndDate!= null">
            AND c.refund_time &lt;= #{refundEndDate,jdbcType=TIMESTAMP}
        </if>
    </select>


</mapper>
