<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.UserRiskRecordMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.UserRiskRecord">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="flowChannel" column="flow_channel" jdbcType="VARCHAR"/>
            <result property="ruleCode" column="rule_code" jdbcType="VARCHAR"/>
            <result property="ruleDesc" column="rule_desc" jdbcType="VARCHAR"/>
            <result property="approveResultCode" column="approve_result_code" jdbcType="VARCHAR"/>
            <result property="approveResult" column="approve_result" jdbcType="VARCHAR"/>
            <result property="approveAmount" column="approve_amount" jdbcType="DECIMAL"/>
            <result property="approveRights" column="approve_rights" jdbcType="VARCHAR"/>
            <result property="riskFinalResult" column="risk_final_result" jdbcType="VARCHAR"/>
            <result property="passTime" column="pass_time" jdbcType="TIMESTAMP"/>
            <result property="expireTime" column="expire_time" jdbcType="DATE"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="revision" column="revision" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
            <result property="riskLevel" column="risk_level" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,flow_channel,
        rule_code,rule_desc,approve_result_code,
        approve_result,approve_amount,approve_rights,
        risk_final_result,pass_time,expire_time,
        remark,revision,created_by,
        created_time,updated_by,updated_time
    </sql>
</mapper>
