package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jinghang.cash.pojo.AppPushConfigRelation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【app_push_config_relation(更新版本关联表)】的数据库操作Mapper
* @createDate 2024-03-22 16:31:05
* @Entity com.jinghang.cash.pojo.AppPushConfigRelation
*/
@Mapper
@DS("slave")
public interface AppPushConfigRelationMapper extends BaseMapper<AppPushConfigRelation> {

}




