package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.cash.modules.manage.vo.req.UserRightsSubscribePagingRequest;
import com.jinghang.cash.modules.manage.vo.res.UserRightsSubscribePagingResponse;
import com.jinghang.cash.pojo.UserRightsSubscribe;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-19
 */
@Mapper
@DS("slave")
public interface UserRightsSubscribeMapper extends BaseMapper<UserRightsSubscribe> {

    List<UserRightsSubscribePagingResponse> paging(@Param("param") UserRightsSubscribePagingRequest request);
}
