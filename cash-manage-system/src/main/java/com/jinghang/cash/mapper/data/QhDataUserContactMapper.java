package com.jinghang.cash.mapper.data;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.cash.pojo.data.QhUserContactInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-08-06
 */
@Mapper
@DS("data")
public interface QhDataUserContactMapper extends BaseMapper<QhUserContactInfo> {

    List<QhUserContactInfo> selectByUserIds(@Param("userIds") Set<String> userIds);
}
