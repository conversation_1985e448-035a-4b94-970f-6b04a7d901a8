package com.jinghang.cash.enums;

/**
 * 待处理/失败/成功/处理中
 */
public enum PayResultEnum {
    WAITING("WAITING", "待支付"),
    SUCCESS("SUCCESS", "成功"),
    FAILED("FAILED", "失败"),
    PROCESSING("PROCESSING", "处理中");

    private String code;
    private String msg;

    PayResultEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getMsg(String code) {
        for (PayResultEnum payResultEnum : PayResultEnum.values()) {
            if (payResultEnum.code.equals(code)) {
                return payResultEnum.msg;
            }
        }
        return code;
    }
}
