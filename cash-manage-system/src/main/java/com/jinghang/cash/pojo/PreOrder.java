package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "pre_order")
public class PreOrder implements Serializable {

    @Serial
    private static final long serialVersionUID = 2905326849640026393L;
    /**
     * 主键
     */
    private String id;
    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 流量渠道
     */
    private String flowChannel;

    /**
     * 用户Id
     */
    private String openId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 用户手机号码
     */
    private String mobile;

    /**
     * 身份证号
     */
    private String certNo;

    /**
     * 订单创建时间
     */
    private Date applyTime;

    /**
     * 申请金额
     */
    private BigDecimal applyAmount;

    /**
     * 申请期限
     */
    private Integer applyPeriods;

    /**
     * 预订单状态
     */
    private String preOrderState;

    private String applicationSource;

    /**
     * 是否已被拒
     */
    private String isReject;

    /**
     * 风控id
     */
    private String riskId;


    private String remark;

    private Integer revision;

    private String createdBy;

    private Date createdTime;

    private String updatedBy;

    private Date updatedTime;
}
