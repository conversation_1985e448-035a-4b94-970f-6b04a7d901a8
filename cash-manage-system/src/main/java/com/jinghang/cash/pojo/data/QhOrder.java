package com.jinghang.cash.pojo.data;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *订单
 */
@Data
@TableName(value = "s03_qh_order")
public class QhOrder implements Serializable {

    private static final long serialVersionUID = 1L;


    private String id;

    private String userId;

    private String flowChannel;

    private String applyChannel;

    private String openId;

    private String outerOrderId;

    private String riskId;

    private Date applyTime;

    private BigDecimal applyAmount;

    private Integer applyPeriods;

    private String loanPurpose;

    private String orderState;

    private Date loanTime;

    private String bankChannel;

    private String loanCardId;

    private String rightsPackageId;

    private String bindCapitalCardState;

    private String orderSubmitState;

    private String rightsMarking;

    private BigDecimal approveAmount;

    private String approveRights;

    private String approveRightsForce;

    private String approveRate;

    private BigDecimal irrRate;

    private BigDecimal monthPay;

    private String name;

    private String mobile;

    private String certNo;

    private String remark;

    private String revision;

    private String createdBy;

    private Date createdTime;

    private String updatedBy;

    private Date updatedTime;

    private BigDecimal rightsAmount;

    private String callZyRisk;

    private String rightsConfigId;

    private String applicationSource;

    private String renewedFlag;

    private String amountType;
}
