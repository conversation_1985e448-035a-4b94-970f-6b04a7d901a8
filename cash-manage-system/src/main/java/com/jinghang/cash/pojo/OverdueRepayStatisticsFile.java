package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghang.cash.mapper.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2024-08-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "overdue_repay_statistics_file")
public class OverdueRepayStatisticsFile extends BaseEntity {

    private LocalDate generateDate;

    private String fileUrl;

    private Integer validDay;

    private String fileName;

    private String ossBucket;

    private String ossKey;

}
