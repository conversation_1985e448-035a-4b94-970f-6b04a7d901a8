package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @TableName custom_repay_record
 */
@TableName(value ="custom_repay_record")
@Data
public class CustomRepayRecord implements Serializable {
    private String id;

    private String outerRepayNo;

    private String loanId;

    private Integer period;

    private Date repayApplyDate;

    private String repayPurpose;

    private String repayMode;

    private BigDecimal principalAmt;

    private BigDecimal interestAmt;

    private BigDecimal guaranteeAmt;

    private BigDecimal penaltyAmt;

    private BigDecimal breachAmt;

    private BigDecimal totalAmt;

    private String agreementNo;

    private String repayState;

    private String failReason;

    private Date repaidDate;

    private String needTwiceState;

    private Date twiceRepaidDate;

    private String remark;

    private String revision;

    private String createdBy;

    private Date createdTime;

    private String updatedBy;

    private Date updatedTime;

    private String paySide;

    private String operationSource;

    private BigDecimal extraGuaranteeAmt;

    private BigDecimal consultFee;

    private static final long serialVersionUID = 1L;
}
