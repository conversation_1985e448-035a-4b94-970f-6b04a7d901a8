package com.jinghang.cash.modules.manage.vo.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.enums.AbleStatus;
import com.jinghang.cash.enums.ProtocolChannel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CapitalConfigInfoResponse implements Serializable {
    private static final long serialVersionUID = 4088875195548548847L;
    /**
     * id
     */
    private String id;
    /**
     * 是否启用
     */
    private String enabled;
    /**
     * 资方
     */
    private String bankChannel;

    /**
     * 融担公司
     */
    private String guaranteeCompany;

    /**
     * 资方利率
     */
    private BigDecimal bankRate;

    /**
     * 资方支持利率
     */
    private String supportIrrLevel;

    /**
     * 资方授信日限额
     */
    private BigDecimal creditDayLimit;

    /**
     * 资方放款日限额
     */
    private BigDecimal loanDayLimit;
    /**
     * 年龄区间
     */
    private String agesRange;

    /**
     * 单笔上下限
     */
    private String singleAmtRange;

    /**
     * 是否开启授信时间范围
     */
    private AbleStatus creditTimeStatus;
    /**
     * 是否开启放款时间范围限制
     */
    private AbleStatus loanTimeStatus;
    /**
     * 是否开启还款时间范围限制
     */
    private AbleStatus repayTimeStatus;


    /**
     * 授信开始HHmmss
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String creditStartTime;

    /**
     * 授信截止HHmmss
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String creditEndTime;

    /**
     * 放款开始HHmmss
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String loanStartTime;

    /**
     * 放款截止HHmmss
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String loanEndTime;

    /**
     * 还款开始时间
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String repayStartTime;

    /**
     * 还款结束时间
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String repayEndTime;
    /**
     * 可选资方
     */
    private List<BankChannelResponse> bankChannelList;
    /**
     * 支持期数，逗号分隔
     */
    private String periodsRange;

    /**
     * 是否可续借
     */
    private String renewedFlag;

    /**
     * 绑卡渠道
     */
    private ProtocolChannel protocolChannel;


}
