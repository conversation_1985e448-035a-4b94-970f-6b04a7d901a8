package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.modules.manage.vo.PageParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * <AUTHOR> gale
 * @Classname DataStatisticsRequest
 * @Description 数据统计请求类
 * @Date 2023/11/20 11:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataStatisticsRequest extends PageParam {

    private LocalDate startDate;

    private LocalDate endDate;
}
