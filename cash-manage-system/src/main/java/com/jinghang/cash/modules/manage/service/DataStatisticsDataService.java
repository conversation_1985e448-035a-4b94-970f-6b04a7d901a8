package com.jinghang.cash.modules.manage.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jinghang.cash.mapper.DataStatisticsMapper;
import com.jinghang.cash.modules.manage.vo.req.DataStatisticsRequest;
import com.jinghang.cash.pojo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> gale
 * @Classname DataStatisticsService
 * @Description 数据统计任务
 * @Date 2023/11/16 19:33
 */
@Service
public class DataStatisticsDataService {

    private static final Logger logger = LoggerFactory.getLogger(DataStatisticsDataService.class);


    @Autowired
    private DataStatisticsMapper dataStatisticsMapper;







    public Page<DataStatistics> queryAll(DataStatisticsRequest request) {
        QueryWrapper<DataStatistics> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .ge(request.getStartDate() != null, DataStatistics::getStatisticDate, request.getStartDate())
                .le(request.getEndDate() != null, DataStatistics::getStatisticDate, request.getEndDate())
                .orderByDesc(DataStatistics::getCreatedTime);
        return PageHelper.startPage(request.getPageNum(), request.getPageSize()).doSelectPage(
                () -> dataStatisticsMapper.selectList(queryWrapper)
        );
    }





}
