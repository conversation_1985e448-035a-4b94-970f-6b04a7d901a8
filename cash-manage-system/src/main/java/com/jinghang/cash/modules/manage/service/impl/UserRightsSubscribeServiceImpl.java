package com.jinghang.cash.modules.manage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jinghang.cash.mapper.UserRightsAuditMapper;
import com.jinghang.cash.mapper.UserRightsSubscribeMapper;
import com.jinghang.cash.modules.manage.service.UserRightsSubscribeService;
import com.jinghang.cash.modules.manage.vo.req.UserRightsSubscribePagingRequest;
import com.jinghang.cash.modules.manage.vo.req.UserRightsUnsubscribeRequest;
import com.jinghang.cash.modules.manage.vo.res.UserRightsSubscribePagingResponse;
import com.jinghang.cash.pojo.UserRightsAudit;
import com.jinghang.cash.pojo.UserRightsSubscribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-02-19
 */
@Service
@Slf4j
public class UserRightsSubscribeServiceImpl extends ServiceImpl<UserRightsSubscribeMapper, UserRightsSubscribe> implements UserRightsSubscribeService {

    @Autowired
    private UserRightsAuditMapper userRightsAuditMapper;

    @Override
    public PageInfo<UserRightsSubscribePagingResponse> paging(UserRightsSubscribePagingRequest request) {
        Page<Object> page = PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<UserRightsSubscribePagingResponse> list = getBaseMapper().paging(request);
        if (list.isEmpty()) {
            return PageInfo.emptyPageInfo();
        }
        Set<String> subscribeIds = list.stream().map(UserRightsSubscribePagingResponse::getId).collect(Collectors.toSet());
        List<UserRightsAudit> userRightsAudits = userRightsAuditMapper.selectList(
            new LambdaQueryWrapper<UserRightsAudit>()
                .in(UserRightsAudit::getSubscribeId, subscribeIds)
                .orderByDesc(UserRightsAudit::getAuditTime)
        );
        Map<String, List<UserRightsAudit>> userSmallRightsAuditsMap = userRightsAudits.stream()
            .collect(Collectors.groupingBy(UserRightsAudit::getSubscribeId));
        for (UserRightsSubscribePagingResponse response : list) {
            List<UserRightsAudit> auditList = userSmallRightsAuditsMap.get(response.getId());
            if (auditList != null && !auditList.isEmpty()) {
                response.setAuditState(auditList.get(0).getAuditState());
                response.setUnsubscribeApplyTime(auditList.get(0).getApplyTime());
                response.setUnsubscribeApplyBy(auditList.get(0).getApplyBy());
                response.setAuditBy(auditList.get(0).getAuditBy());
                response.setUnsubscribeReason(auditList.get(0).getReason());
            }
        }
        PageInfo<UserRightsSubscribePagingResponse> pageInfo = new PageInfo<>(list);
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        return pageInfo;
    }

    @Override
    public boolean cancel(UserRightsUnsubscribeRequest request) {
        // 调用cash-business 申请取消订阅
//        RightsSubscribeCancelApplyDTO applyDTO = new RightsSubscribeCancelApplyDTO();
//        applyDTO.setId(request.getId());
//        applyDTO.setReason(request.getReason());
//        applyDTO.setOperator(SecurityUtils.getCurrentUsername());
//        RestResult<Boolean> result = rightsSubscribeFeignService.cancelApply(applyDTO);
//        if (result == null || !result.isSuccess()) {
//            String reason = result == null ? "系统异常" : result.getMsg();
//            log.error("小卡订阅取消申请失败, reason: {}", reason);
//            throw new RuntimeException(reason);
//        }
        return Boolean.TRUE;
    }
}
