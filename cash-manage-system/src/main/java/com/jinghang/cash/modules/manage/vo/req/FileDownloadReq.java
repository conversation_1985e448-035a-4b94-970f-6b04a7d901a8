package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.capital.api.dto.FileType;
import com.jinghang.cash.enums.BankChannel;
import com.jinghang.cash.modules.manage.vo.PageParam;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

/**
 * 订单详情请求参数
 */
@Data
public class FileDownloadReq extends PageParam {

    /**
     * 手机号
     */
    private String orderId;
    /**
     * 订单号
     */
    @Enumerated(EnumType.STRING)
    private FileType type;

    /**
     * 资方类型
     */
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public FileType getType() {
        return type;
    }

    public void setType(FileType type) {
        this.type = type;
    }
}
