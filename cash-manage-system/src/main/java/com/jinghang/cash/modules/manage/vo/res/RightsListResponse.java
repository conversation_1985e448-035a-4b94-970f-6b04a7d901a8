package com.jinghang.cash.modules.manage.vo.res;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 获取权益包
 *
 * <AUTHOR>
 * @date 2024/4/18
 */
@Data
public class RightsListResponse {
    /**
     * 权益类型包id
     */
    private String rightsId;
    /**
     * 权益包类型
     */
    private String rightsType;
    /**
     * 成本价
     */
    private BigDecimal costingPrice;
    /**
     * 是否是兜底权益
     */
    private String bottom;
    /**
     * 权益开关
     */
    private String approveRights;
    /**
     * 是否强制购买
     */
    private String approveRightsForce;


}
