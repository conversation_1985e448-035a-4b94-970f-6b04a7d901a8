package com.jinghang.cash.modules.manage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.convert.AggregatePayConvert;
import com.jinghang.cash.enums.ResultCode;
import com.jinghang.cash.mapper.AggregatePaymentConfigMapper;
import com.jinghang.cash.modules.manage.service.AggregatePaymentConfigService;
import com.jinghang.cash.modules.manage.vo.req.AggregatePaymentConfigRequest;
import com.jinghang.cash.modules.manage.vo.rsp.AggregatePaymentConfigResp;
import com.jinghang.cash.pojo.AggregatePaymentConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class AggregatePaymentConfigServiceImpl extends ServiceImpl<AggregatePaymentConfigMapper, AggregatePaymentConfig> implements AggregatePaymentConfigService {
    private static final Logger logger = LoggerFactory.getLogger(AggregatePaymentConfigService.class);

    @Autowired
    private AggregatePaymentConfigMapper aggregatePaymentConfigMapper;

    @Override
    public List<AggregatePaymentConfigResp> queryAllInfo() {
        List<AggregatePaymentConfigResp> resps = new ArrayList<>();
        LambdaQueryWrapper<AggregatePaymentConfig> wrapper = new LambdaQueryWrapper<>();
        List<AggregatePaymentConfig> configs = aggregatePaymentConfigMapper.selectList(wrapper);
        for (AggregatePaymentConfig config : configs) {
            resps.add(AggregatePayConvert.INSTANCE.toAggregatePaymentConfigResp(config));
        }
        return resps;
    }

    @Override
    public ResultCode update(AggregatePaymentConfigRequest request) {
        AggregatePaymentConfig config = AggregatePayConvert.INSTANCE.toAggregatePaymentConfig(request);
        aggregatePaymentConfigMapper.updateById(config);
        return ResultCode.SUCCESS;
    }
}
