package com.jinghang.capital.batch.domain.cybk;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileMode;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.batch.domain.product.LoanReccDto;
import com.jinghang.capital.batch.entity.CYBKReccLoan;
import com.jinghang.capital.batch.entity.CYBKReconcileFile;
import com.jinghang.capital.batch.entity.Loan;
import com.jinghang.capital.batch.entity.ReconciliationFile;
import com.jinghang.capital.batch.enums.ReccStateEnum;
import com.jinghang.capital.batch.service.FileService;
import com.jinghang.capital.batch.service.cybk.CYBKReccLoanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 放款对账
 * <p>
 * 流水号|放款编号|放款完成日期|放款期数|放款成功金额|放款结果|资产池编号
 * RN230825154141780679103841725418|LA1HXXR1KIMF400|20230825|12|15000.00|00|APF20Q65EY1M
 */
@Component
public class CYBKLoanFileHandler extends AbstractCYBKReconFileHandler<CYBKReccLoan> {

    private static final Logger logger = LoggerFactory.getLogger(CYBKLoanFileHandler.class);

    private static final int LOAN_ID_IDX = 0;
    private static final int CUST_ID_IDX = 1;
    private static final int LOAN_APPLY_TIME_IDX = 2;
    private static final int LOAN_APPLY_ID_IDX = 3;
    private static final int LOAN_CONTRACT_NO_IDX = 4;
    private static final int LOAN_NO_IDX = 5;
    private static final int AMOUNT_IDX = 6;
    private static final int PERIOD_IDX = 7;
    private static final int RATE_IDX = 8;
    private static final int LOAN_TIME_IDX = 9;
    private static final int DUE_DATE_IDX = 10;
    private static final int NAME_IDX = 11;
    private static final int BANK_IDX = 12;
    private static final int ACCT_IDX = 13;


    //  /upload/cyxf/{产品编码}/in/files/{YYYYMMDD}/loan_${yyyyMMdd}.csv
    // 长银借据明细文件
    private static final String RECC_FILE_NAME = "%s/loan_%s.csv";
    private static final Integer LINE_LENGTH = 14;

    @Autowired
    private CYBKReccLoanService reccLoanService;


    @Autowired
    private FileService fileService;


    @Override
    protected String ossFilePath(LocalDate data) {
        return "cybk/recc/loan/" + getFileName(data);
    }

    @Override
    protected String sftpFilePath(LocalDate data) {
        return getSftpPathPrefix() + "/" + getFileName(data);
    }

    private String getFileName(LocalDate data) {
        String dateStr1 = data.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return RECC_FILE_NAME.formatted(dateStr1, dateStr1);
    }

    @Override
    protected List<CYBKReccLoan> getReccFileDetails(InputStream inputStream, StringBuffer sftpFileStr) {
        List<CYBKReccLoan> entityList = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            int lineNum = 0;
            while ((line = reader.readLine()) != null) {
                line = line.trim();

                if (line.isEmpty()) {
                    continue;
                }
                lineNum++;
                if (lineNum == 1) {
                    continue;
                }
                appendStr(sftpFileStr, line);
                String[] lineArr = line.split(SEPARATOR, LINE_LENGTH);
                if (lineArr.length < LINE_LENGTH) {
                    throw new RuntimeException("长银消金直连解析数据异常,line:" + (entityList.size() + 1) + "__" + line);
                }

                String loanNo = lineArr[LOAN_ID_IDX];

                if (!loanNo.startsWith("LO")) {
                    logger.info("reccLoan loanId: [{}] 非本平台固有借据号格式 忽略", loanNo);
                    continue;
                }
                Loan loan = getLoanService().findByIdAndChannel(loanNo, BankChannel.CYBK.name());
                if (Objects.isNull(loan)) {
                    logger.info("reccLoan loanId: [{}] 未查到数据库匹配信息 忽略", loanNo);
                    continue;
                }

                //保存长银消金直连放款数据
                CYBKReccLoan entity = new CYBKReccLoan();
                if (loan != null) {
                    entity.setSysId(loan.getId());
                }
                entity.setOutAppSeq(lineArr[LOAN_ID_IDX]);
                entity.setCustId(lineArr[CUST_ID_IDX]);
                entity.setApplyDt(lineArr[LOAN_APPLY_TIME_IDX]);
                entity.setApplSeq(lineArr[LOAN_APPLY_ID_IDX]);
                entity.setContNo(lineArr[LOAN_CONTRACT_NO_IDX]);
                entity.setBasicIntRat(lineArr[RATE_IDX]);
                entity.setAmount(new BigDecimal(lineArr[AMOUNT_IDX]));
                entity.setLoanNo(loanNo);
                entity.setPeriod(Integer.valueOf(lineArr[PERIOD_IDX]));
                entity.setLoanTime(LocalDate.parse(lineArr[LOAN_TIME_IDX], DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                entity.setChannel(BankChannel.CYBK.name());
                entityList.add(entity);
            }
        } catch (Exception e) {
            logger.error("cybk loan file recc error.", e);
            throw new RuntimeException("长银消金直连解析文件失败", e);
        }
        return entityList;
    }


    public void processTszRecFile(LocalDate fileDate, List<CYBKReccLoan> entityList) {
        List<LoanReccDto> result = new ArrayList<>();
        for (CYBKReccLoan reccLoan : entityList) {
            Loan loan = getLoanService().findByIdAndChannel(reccLoan.getSysId(), BankChannel.CYBK.name());
            if (loan != null) {
                LoanReccDto tszRecc = toTszRecc(reccLoan);
                tszRecc.setOutLoanId(loan.getOuterLoanId());
                tszRecc.setChannel(BankChannel.CYBK.name());
                result.add(tszRecc);
            }
        }

        try {
            Path tempFile = Files.createTempFile("tsz_loan_recc", "txt");
            BufferedWriter writer = Files.newBufferedWriter(tempFile);
            for (LoanReccDto dto : result) {
                writer.write(dto.getFormattedTxt());
                writer.newLine();
            }
            writer.close();

            String targetFileName = fileDate.format(DateTimeFormatter.BASIC_ISO_DATE) + "_loanCheck.txt";
            String ossPath = "tsz/recc/cybk/loan/" + targetFileName;
            fileService.switchOss(ossBucketName()).putObject(ossBucketName(), ossPath, Files.newInputStream(tempFile));

            ReconciliationFile tszRecFile = new ReconciliationFile();

            tszRecFile.setProduct(Product.ZC_CASH);
            tszRecFile.setBankChannel(BankChannel.CYBK);
            tszRecFile.setFileType(FileType.LOAN_FILE);
            tszRecFile.setFileName(targetFileName);
            tszRecFile.setFileDate(fileDate);
            tszRecFile.setMode(FileMode.OSS);
            tszRecFile.setReconciliationState(ReccStateEnum.P);
            tszRecFile.setTargetOssBucket(ossBucketName());
            tszRecFile.setTargetOssKey(ossPath);

            getReconciliationFileService().save(tszRecFile);

            Files.delete(tempFile);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    private LoanReccDto toTszRecc(CYBKReccLoan reccLoan) {
        LoanReccDto dto = new LoanReccDto();
        dto.setLoanId(reccLoan.getSysId());
        dto.setAmount(reccLoan.getAmount());
        dto.setPeriod(reccLoan.getPeriod());
        dto.setLoanTime(reccLoan.getLoanTime());
        dto.setStatus(reccLoan.getStatus());
        dto.setRemark(reccLoan.getRemark());
        return dto;
    }

    @Override
    protected void saveEntity(List<CYBKReccLoan> entityList) {
        reccLoanService.saveBatch(entityList, DEFAULT_BATCH_SIZE);
    }

    @Override
    protected void fillEntityData(List<CYBKReccLoan> entityList, CYBKReconcileFile reconcileFile) {
        entityList.forEach(e -> {
            e.setReccStatus(null);
            e.setReccId(reconcileFile.getId());
            e.setCreatedTime(LocalDateTime.now());
            e.setUpdatedTime(LocalDateTime.now());
        });

    }


    @Override
    public CYBKReccFileTypeEnum getReccType() {
        return CYBKReccFileTypeEnum.LOAN_FILE;
    }
}
