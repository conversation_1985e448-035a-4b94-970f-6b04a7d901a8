# HXBK回调接口加解密工具使用说明

## 概述

本工具包专门用于处理蚂蚁天枢系统调用我方回调接口时的加解密需求。我方作为被调用方，需要：
1. 接收蚂蚁侧的加密请求并解密
2. 处理业务逻辑
3. 构建加密响应返回给蚂蚁侧

## 业务场景

蚂蚁侧会调用我方的回调接口，典型场景包括：
- 授信结果通知
- 放款结果通知  
- 还款结果通知
- 其他业务状态变更通知

## 处理流程

```
蚂蚁侧请求 → 我方接口 → 解密验签 → 业务处理 → 加密响应 → 返回蚂蚁侧
```

## 核心工具类

### 1. HXBKCryptoService - 回调处理服务（推荐使用）
专门为回调接口设计的服务类，封装了完整的处理流程：

- `processIncomingRequest(String requestStr, Class<T> targetClass)` - 解密蚂蚁侧请求
- `buildSuccessResponse(Object bizData)` - 构建成功响应
- `buildFailResponse(String code, String msg)` - 构建失败响应

### 2. 底层工具类（高级用法）
如需自定义处理流程，可直接使用：

- **AESUtils** - AES业务数据加解密
- **RSAUtils** - RSA密钥加解密和数字签名验签  
- **ParamUtils** - 参数解析和响应构建

## 配置说明

### 1. 密钥配置
在`application.yml`中配置加解密所需的密钥：

```yaml
hxbk:
  crypto:
    partner:
      privateKey: ${HXBK_PARTNER_PRIVATE_KEY}  # 我方私钥（用于解密蚂蚁侧发来的SecretKey）
    ant:
      publicKey: ${HXBK_ANT_PUBLIC_KEY}        # 蚂蚁侧公钥（用于验签和加密响应）
```

### 2. 环境变量设置
```bash
# 我方私钥（使用RSA2048KeyPairGenerator生成，对应的公钥需提供给蚂蚁侧）
export HXBK_PARTNER_PRIVATE_KEY="MIIEvQIBADANBgkqhkiG9w0BAQEFAASC..."

# 蚂蚁侧公钥（由蚂蚁侧提供）
export HXBK_ANT_PUBLIC_KEY="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A..."
```

### 3. 密钥生成
使用工具生成我方密钥对：
```java
// 运行密钥生成器
RSA2048KeyPairGenerator.main(new String[]{});
```
生成后将公钥提供给蚂蚁侧，私钥配置到我方系统。

## 使用示例

### 1. 推荐方式：使用HXBKCryptoService

```java
@RestController
@RequestMapping("/hxbk/callback")
public class HXBKCallbackController {
    
    @Autowired
    private HXBKCryptoService cryptoService;
    
    /**
     * 授信结果回调接口
     */
    @PostMapping("/credit/result")
    public String creditResultCallback(@RequestBody String requestBody) {
        try {
            // 步骤1：解密验签蚂蚁侧请求
            CreditResultNotifyRequest request = cryptoService.processIncomingRequest(
                requestBody, CreditResultNotifyRequest.class);
            
            // 步骤2：处理业务逻辑
            processCreditResult(request);
            
            // 步骤3：构建成功响应
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("result", "success");
            responseData.put("timestamp", System.currentTimeMillis());
            
            return cryptoService.buildSuccessResponse(responseData);
            
        } catch (Exception e) {
            logger.error("处理授信结果回调失败", e);
            return cryptoService.buildFailResponse("500000", "处理失败");
        }
    }
    
    /**
     * 放款结果回调接口
     */
    @PostMapping("/loan/result")
    public String loanResultCallback(@RequestBody String requestBody) {
        try {
            // 解密请求
            LoanResultNotifyRequest request = cryptoService.processIncomingRequest(
                requestBody, LoanResultNotifyRequest.class);
            
            // 处理业务逻辑
            processLoanResult(request);
            
            // 构建响应
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("result", "processed");
            
            return cryptoService.buildSuccessResponse(responseData);
            
        } catch (Exception e) {
            logger.error("处理放款结果回调失败", e);
            return cryptoService.buildFailResponse("500000", "处理失败");
        }
    }
    
    // 业务处理方法
    private void processCreditResult(CreditResultNotifyRequest request) {
        // 根据request中的数据更新授信状态
        logger.info("处理授信结果：{}", request.getApplySerialNo());
    }
    
    private void processLoanResult(LoanResultNotifyRequest request) {
        // 根据request中的数据更新放款状态
        logger.info("处理放款结果：{}", request.getLoanSerialNo());
    }
}
```

## 处理流程详解

### 接收蚂蚁侧请求的处理步骤：

1. **接收加密请求**：蚂蚁侧POST请求到我方回调接口
2. **验证签名**：使用蚂蚁侧公钥验证请求签名
3. **解密SecretKey**：使用我方私钥解密蚂蚁侧发来的SecretKey
4. **解密业务数据**：使用SecretKey解密业务数据
5. **处理业务逻辑**：根据解密后的数据执行相应业务处理
6. **构建响应**：生成新的SecretKey，加密响应数据并签名
7. **返回响应**：返回加密后的响应给蚂蚁侧

### 关键点说明：

- **验签用蚂蚁侧公钥**：确保请求确实来自蚂蚁侧
- **解密用我方私钥**：解密蚂蚁侧用我方公钥加密的SecretKey
- **响应加密用蚂蚁侧公钥**：确保只有蚂蚁侧能解密响应
- **响应签名用我方私钥**：证明响应来自我方

## 错误处理

### 常见错误码：
- `100000` - 验签失败
- `200000` - 解密失败  
- `500000` - 系统异常

### 异常处理示例：
```java
try {
    // 处理请求
    return cryptoService.buildSuccessResponse(responseData);
} catch (RuntimeException e) {
    if (e.getMessage().contains("验签失败")) {
        return cryptoService.buildFailResponse("100000", "验签失败");
    } else if (e.getMessage().contains("解密失败")) {
        return cryptoService.buildFailResponse("200000", "解密失败");
    } else {
        return cryptoService.buildFailResponse("500000", "系统异常");
    }
}
```

## 测试验证

### 1. 生成测试密钥对
```java
RSA2048KeyPairGenerator.main(new String[]{});
```

### 2. 测试完整流程
```java
CryptoDemo.testFullCryptoFlow();
```

### 3. 模拟蚂蚁侧请求测试
```java
// 使用CryptoDemo.acceptAntRequest()方法测试
String mockRequest = "..."; // 模拟的蚂蚁侧加密请求
String response = CryptoDemo.acceptAntRequest(mockRequest);
```

## 注意事项

1. **密钥安全**：我方私钥严格保密，不能泄露
2. **配置正确**：确保蚂蚁侧公钥配置正确
3. **异常处理**：所有异常都要妥善处理，不能暴露敏感信息
4. **日志记录**：记录处理过程但不记录敏感数据
5. **性能考虑**：回调接口要快速响应，避免超时

## 部署清单

部署前确认以下配置：
- ✅ 我方私钥已配置到环境变量
- ✅ 蚂蚁侧公钥已正确配置
- ✅ 我方公钥已提供给蚂蚁侧
- ✅ 回调接口URL已提供给蚂蚁侧
- ✅ 网络连通性已验证
