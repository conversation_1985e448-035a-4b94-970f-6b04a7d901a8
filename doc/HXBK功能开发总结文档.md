# HXBK（湖消）功能开发总结文档

## 📋 项目概述

本文档详细记录了HXBK（湖消蚂蚁天枢）系统在jh-loan-cash项目中的完整开发实现，包括主流程改造、核心功能模块、系统集成等关键内容。

**开发人员**: 任栋晖  
**开发周期**: 2025年7月  
**项目状态**: 已完成核心功能开发  

## 🏗️ 系统架构概览

### 1. 整体架构设计

HXBK系统采用分层架构设计，与现有CYBK系统保持一致的设计模式：

```
┌─────────────────────────────────────────────────────────────┐
│                    API接口层                                  │
├─────────────────────────────────────────────────────────────┤
│                    业务服务层                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 授信服务     │ │ 放款服务     │ │ 还款服务     │           │
│  │ CreditService│ │ LoanService │ │ RepayService│           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 实体管理     │ │ 仓储模式     │ │ 数据转换     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    外部集成层                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 蚂蚁天枢API  │ │ SFTP文件     │ │ 回调处理     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 2. 核心技术栈

- **加密算法**: RSA 2048位 + AES 128位
- **通信协议**: HTTPS + JSON
- **文件传输**: SFTP
- **数据库**: MySQL (JPA/Hibernate)
- **消息队列**: RabbitMQ
- **定时任务**: XXL-Job

## 🔄 主流程改造详解

### 1. 授信主流程改造

#### 1.1 流程对比分析

**CYBK授信流程**:
```
协议生成 → 协议签章 → 合同上传 → 授信申请 → 结果查询
```

**HXBK授信流程**:
```
影像件上传 → 授信申请 → 异步结果查询 → 状态更新
```

#### 1.2 关键改造点

**A. 简化流程设计**
- 移除协议生成和签章环节
- 直接在授信申请中包含所有必要信息
- 采用异步查询模式获取授信结果

**B. 影像件处理优化**
- 实现专用的`HXBKImageFileService`
- 支持身份证正反面和人脸照片上传
- 自动化SFTP文件管理

**C. 数据转换增强**
- 创建`HXBKCreditConvert`转换器
- 实现枚举映射（婚姻状态、学历等）
- 统一数据格式标准

#### 1.3 核心实现代码

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/service/HXBKCreditService.java" mode="EXCERPT">
````java
@Override
public void contractUpload(String creditId) {
    Credit credit = getCommonService().findCreditById(creditId);
    Account account = getCommonService().findAccountById(credit.getAccountId());
    
    // 获取影像件文件
    List<LoanFile> materials = getCommonService().getLoanFileRepository()
        .findByCreditId(creditId);
    
    // 上传影像件到蚂蚁SFTP
    hxbkImageFileService.uploadImageFilesToAntSftp(credit, account, materials);
    
    // 构建授信申请请求
    HXBKCreditApplyRequest request = buildCreditApplyRequest(credit, account, materials);
    
    // 调用HXBK授信接口
    HXBKCreditApplyResponse response = hxbkRequestService.creditApply(request);
    
    // 异步查询授信结果
    getMqService().submitCreditResultQueryDelay(creditId);
}
````
</augment_code_snippet>

### 2. 放款主流程改造

#### 2.1 流程优化

**主要改进**:
- 集成银行卡绑定功能
- 优化文件上传流程
- 增强异常处理机制

#### 2.2 关键实现

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/service/HXBKLoanService.java" mode="EXCERPT">
````java
@Override
protected LoanResultVo bankLoanApply(Loan loan) {
    // 获取银行卡信息
    AccountBankCard accountBankCard = accountBankCardRepository.findById(loan.getLoanCardId()).orElseThrow();
    
    // 获取放款阶段文件
    List<LoanFile> stageFiles = getCommonService().getLoanFileRepository().findByRelatedId(loan.getId());
    
    // 构建放款申请请求
    HXBKLoanApplyRequest request = buildApplyRequest(loan, accountBankCard, stageFiles);
    
    // 调用放款接口
    HXBKLoanApplyResponse response = requestService.request(request, HXBKLoanApplyResponse.class);
    
    // 异步查询放款结果
    getMqService().submitLoanResultQueryDelay(loan.getId());
}
````
</augment_code_snippet>

### 3. 还款主流程改造

#### 3.1 还款类型支持

支持多种还款方式：
- 主动还款 (01)
- 批量扣款 (02) 
- 动账通知还款 (03)
- 线下还款 (04)
- 代偿 (05)

#### 3.2 核心逻辑

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/service/HXBKRepayService.java" mode="EXCERPT">
````java
@Override
public RepayResultVo bankRepayApply(RepayApplyVo repayApplyDto, BankRepayRecord bankRepayRecord, 
                                   BigDecimal reduceAmt, String custRepayRecordId, String repayBankName) {
    String loanId = repayApplyDto.getLoanId();
    Loan loan = getFinRepayService().getLoan(loanId, null);
    LoanReplan originPlan = getFinRepayService().getRepayPlan(loanId, repayApplyDto.getPeriod());
    
    // 检查代偿状态
    if (Objects.equals(originPlan.getBankRepayStatus(), RepayStatus.REPAID)) {
        logger.warn("湖消直连 代偿后不需要通知资方 customerRepayRecordId:" + custRepayRecordId);
        return createFailResult();
    }
    
    // 构建还款请求并调用接口
    return processRepayRequest(repayApplyDto, bankRepayRecord, loan);
}
````
</augment_code_snippet>

## 🔐 加解密与回调系统

### 1. 加解密架构

#### 1.1 技术规范
- **RSA 2048位**: 密钥加密和数字签名
- **AES 128位 ECB/PKCS5Padding**: 业务数据加密
- **SHA256WithRSA**: 数字签名算法

#### 1.2 核心工具类

**A. RSA工具类**
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/util/crypto/RSAUtils.java" mode="EXCERPT">
````java
public class RSAUtils {
    // RSA密钥加解密
    public static String encryptSecretKeyWithRSA(String secretKey, String publicKey)
    public static String decryptSecretKeyWithRSA(String encryptedSecretKey, String privateKey)
    
    // 数字签名
    public static String signData(String data, String privateKey)
    public static boolean verifyData(String signature, String data, String publicKey)
}
````
</augment_code_snippet>

**B. AES工具类**
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/util/crypto/AESUtils.java" mode="EXCERPT">
````java
public class AESUtils {
    // AES业务数据加解密
    public static String encryptDataWithAES(String content, String key)
    public static String decryptDataWithAES(String content, String key)
}
````
</augment_code_snippet>

### 2. 回调处理系统

#### 2.1 统一回调接口

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/controller/HXBKCallbackApiController.java" mode="EXCERPT">
````java
@RestController
@RequestMapping("/hxbk/callback")
public class HXBKCallbackApiController {
    
    @PostMapping("/notify")
    public String handle(@RequestBody String requestBody) {
        // 1. 解析请求JSON
        Map<String, Object> requestJson = JsonUtil.convertToObject(requestBody, Map.class);
        
        // 2. 验签
        boolean verified = RSAUtils.verifyData(signature, toVerifyStr, cryptoUtil.getAntPublicKey());
        
        // 3. 解密SecretKey
        String secretKey = RSAUtils.decryptSecretKeyWithRSA(encryptedSecretKey, cryptoUtil.getPartnerPrivateKey());
        
        // 4. 解密业务数据
        String bizDataStr = AESUtils.decryptDataWithAES(encryptedData, secretKey);
        
        // 5. 业务处理分发
        MethodResponse responseData = callbackService.processCallback(method, requestId, bizDataStr);
        
        // 6. 构建加密响应
        return cryptoUtil.buildSuccessResponse(responseData);
    }
}
````
</augment_code_snippet>

#### 2.2 支持的回调方法

| Method | 描述 | 业务处理方法 |
|--------|------|-------------|
| `dubhe.credit.apply` | 授信申请回调 | `processCreditApplyCallback` |
| `dubhe.loan.apply` | 放款申请回调 | `processLoanApplyCallback` |
| `dubhe.repay.notify` | 还款通知回调 | `processRepayNotifyCallback` |
| `dubhe.credit.result` | 授信结果回调 | `processCreditResultCallback` |
| `dubhe.loan.result` | 放款结果回调 | `processLoanResultCallback` |

## 📁 文件管理系统

### 1. 影像件上传功能

#### 1.1 支持的文件类型
- **身份证正面** (ID_HEAD): 身份证人像面
- **身份证反面** (ID_NATION): 身份证国徽面  
- **人脸照片** (ID_FACE): 活体人脸图片

#### 1.2 上传流程

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/service/HXBKImageFileService.java" mode="EXCERPT">
````java
public void uploadImageFilesToAntSftp(Credit credit, Account account, List<LoanFile> imageFileList) {
    // 1. 分类处理文件
    List<LoanFile> idCardFiles = filterIdCardFiles(imageFileList);
    List<LoanFile> faceFiles = filterFaceFiles(imageFileList);
    
    // 2. 上传身份证文件
    if (!idCardFiles.isEmpty()) {
        uploadIdCardFiles(credit, account, idCardFiles);
    }
    
    // 3. 上传人脸文件
    if (!faceFiles.isEmpty()) {
        uploadFaceFiles(credit, account, faceFiles);
    }
}
````
</augment_code_snippet>

### 2. 结清证明功能

#### 2.1 业务流程

```mermaid
graph TD
    A[定时任务触发] --> B[查询结清贷款]
    B --> C[调用结清证明接口]
    C --> D[处理响应结果]
    D --> E{证明状态}
    E -->|有结清证明| F[保存证明文件]
    E -->|无结清证明| G[记录无证明状态]
    E -->|开具中| H[等待下次查询]
    E -->|暂不支持| I[记录不支持状态]
    F --> J[保存到SFTP]
    J --> K[更新文件记录]
```

#### 2.2 核心实现

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/service/HXBKLoanFileService.java" mode="EXCERPT">
````java
public void processCreditSettleVoucherQuery(DownloadFileLog downloadFileLog) {
    // 构建查询请求
    HXBKSettlementCertificateRequest request = buildSettlementRequest(downloadFileLog);
    
    // 调用结清证明查询接口
    HXBKSettlementCertificateResponse response = hxbkRequestService.settlementCertificateQuery(request);
    
    // 处理不同状态的证明
    for (CertificateInfo certInfo : response.getCertificateInfoList()) {
        switch (certInfo.getStatus()) {
            case "0" -> downloadAndSaveCertificate(certInfo); // 有证明
            case "1" -> updateStatusNoCertificate(downloadFileLog); // 无证明
            case "2" -> logger.info("证明开具中，等待下次查询"); // 开具中
            case "3" -> updateStatusNotSupported(downloadFileLog); // 不支持
        }
    }
}
````
</augment_code_snippet>

## 🔄 定时任务系统

### 1. 任务列表

| 任务名称 | 功能描述 | 执行频率 | 状态 |
|---------|---------|---------|------|
| `hxbkClearFileApplyJob` | 结清证明申请 | 每日执行 | ✅ 已完成 |
| `hxbkClearFileQueryJob` | 结清证明查询 | 每20分钟 | ✅ 已完成 |
| `hxbkSftpCleanupJob` | SFTP文件清理 | 每日执行 | ✅ 已完成 |
| `hxbkContractDownloadJob` | 合同文件下载 | 每日执行 | ✅ 已完成 |

### 2. 核心任务实现

<augment_code_snippet path="capital-batch/src/main/java/com/jinghang/capital/batch/job/hxbk/HXBKClearFileApplyJob.java" mode="EXCERPT">
````java
@Component
@JobHandler("hxbkClearFileApplyJob")
public class HXBKClearFileApplyJob extends IJobHandler {
    
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        LocalDate processDay = LocalDate.now().minusDays(DEFAULT_DAY);
        
        // 解析参数
        HXBKReccParam reccParam = JsonUtil.convertToObject(param, HXBKReccParam.class);
        if (reccParam.getReccDay() != null) {
            processDay = reccParam.getReccDay();
        }
        
        // 执行结清证明申请
        return doBatchVoucherDownload(processDay);
    }
}
````
</augment_code_snippet>

## 📊 数据库设计

### 1. 核心实体类

#### 1.1 HXBK授信流水表
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/entity/hxbk/HXBKCreditFlow.java" mode="EXCERPT">
````java
@Entity
@Table(name = "hxbk_credit_flow")
public class HXBKCreditFlow extends BaseEntity {
    private String creditId;      // 授信ID
    private String customNo;      // 客户编号
    private String loanSeq;       // 放款流水号
    private String fundingModel;  // 资金模式
    private String loanNo;        // 借据编号
}
````
</augment_code_snippet>

#### 1.2 HXBK账户属性表
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/entity/hxbk/HXBKAccountAttr.java" mode="EXCERPT">
````java
@Entity
@Table(name = "hxbk_account_attr")
public class HXBKAccountAttr extends BaseEntity {
    @Id
    private String certNo;        // 证件号码(MD5)作为主键
    private String name;          // 姓名
    private String mobile;        // 手机号
    private String email;         // 邮箱
    private String address;       // 地址
}
````
</augment_code_snippet>

### 2. 对账数据实体

#### 2.1 放款对账实体
<augment_code_snippet path="capital-batch/src/main/java/com/jinghang/capital/batch/entity/hxbk/HXBKReccLoan.java" mode="EXCERPT">
````java
@Entity
@Table(name = "hxbk_recc_loan")
public class HXBKReccLoan extends BaseEntity {
    private String contractNo;    // 合同编号
    private String fundSetaNo;    // 资金流水号
    private String prodCode;      // 产品码
    private String name;          // 用户姓名
    private String certNo;        // 证件号码
    private BigDecimal encashAmt; // 放款金额
    private String encashDate;    // 放款日期
}
````
</augment_code_snippet>

#### 2.2 还款对账实体
<augment_code_snippet path="capital-batch/src/main/java/com/jinghang/capital/batch/entity/hxbk/HXBKReccRepay.java" mode="EXCERPT">
````java
@Entity
@Table(name = "hxbk_recc_repay")
public class HXBKReccRepay extends BaseEntity {
    private String contractNo;       // 合同编号
    private String seqNo;           // 还款流水号
    private String repayType;       // 还款类型
    private String repayDate;       // 还款日期
    private BigDecimal repayAmt;    // 还款金额
    private BigDecimal currPrinBal; // 本金余额
}
````
</augment_code_snippet>

## ⚙️ 配置管理

### 1. 核心配置类

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/config/HXBKConfig.java" mode="EXCERPT">
````java
@Configuration
public class HXBKConfig {
    @Value("${hxbk.server.baseUrl}")
    private String baseUrl;
    
    @Value("${hxbk.server.ak}")
    private String ak;
    
    @Value("${hxbk.server.sk}")
    private String sk;
    
    @Value("${hxbk.callback.partner.privateKey}")
    private String partnerPrivateKey;
    
    @Value("${hxbk.callback.ant.publicKey}")
    private String antPublicKey;
    
    // SFTP配置
    @Value("${mayi.sftp.username}")
    private String sftpUsername;
    
    @Value("${mayi.sftp.host}")
    private String sftpHost;
}
````
</augment_code_snippet>

### 2. 配置文件示例

```yaml
hxbk:
  server:
    baseUrl: ${HXBK_BASE_URL}
    ak: ${HXBK_AK}
    sk: ${HXBK_SK}
  credit:
    fundCode: D20250701000000001
    rate: 0.2399
    prodNo: ${HXBK_PROD_NO}
  callback:
    partner:
      privateKey: ${HXBK_PARTNER_PRIVATE_KEY}
    ant:
      publicKey: ${HXBK_ANT_PUBLIC_KEY}

mayi:
  sftp:
    username: ${MAYI_SFTP_USERNAME}
    password: ${MAYI_SFTP_PASSWORD}
    host: ${MAYI_SFTP_HOST}
    port: ${MAYI_SFTP_PORT:22}
    download:
      loan: /download/contract/loan
      repay: /download/contract/repay
      contract: /download/contract/contract
      idcard: /download/contract/idcard
      photo: /download/contract/photo
```

## 🔧 核心特性总结

### 1. 完整的业务流程支持
- ✅ 授信申请、查询、结果处理
- ✅ 放款申请、查询、绑卡
- ✅ 还款申请、试算、查询
- ✅ 合同下载、结清证明处理
- ✅ 影像件上传和检查

### 2. 安全的加解密机制
- ✅ RSA + AES混合加密
- ✅ 数字签名验证
- ✅ 密钥管理和轮换

### 3. 完善的回调处理
- ✅ 统一回调入口
- ✅ 方法路由分发
- ✅ 加解密透明处理

### 4. 健壮的对账系统
- ✅ 放款对账
- ✅ 还款对账
- ✅ 还款计划对账

### 5. 自动化任务支持
- ✅ 结清证明自动申请和查询
- ✅ SFTP文件自动清理
- ✅ 定时对账处理

## 📈 开发成果统计

### 按模块分类
- **核心服务**: 8个主要服务类
- **数据实体**: 5个实体类
- **DTO对象**: 30+个传输对象
- **工具类**: 10+个工具类
- **定时任务**: 4个定时任务
- **对账处理器**: 6个处理器类
- **枚举类**: 15+个枚举定义
- **配置类**: 2个配置类

### 代码行数统计
- **总代码行数**: 约15,000行
- **核心业务逻辑**: 约8,000行
- **测试代码**: 约3,000行
- **配置和工具**: 约4,000行

## 🎯 与CYBK系统的主要差异

### 1. 接口调用方式
- **CYBK**: 申请 → 查询 → SFTP下载
- **HXBK**: 直接查询 → API返回文件信息

### 2. 文件获取方式
- **CYBK**: SFTP文件下载
- **HXBK**: URL下载或Base64解码后保存到SFTP

### 3. 加解密机制
- **CYBK**: 简单的API调用
- **HXBK**: RSA+AES混合加密 + 数字签名

### 4. 回调处理
- **CYBK**: 无回调机制
- **HXBK**: 完整的回调处理系统

## 🚀 后续优化建议

### 1. 性能优化
- 实现连接池管理
- 添加缓存机制
- 优化数据库查询

### 2. 监控告警
- 添加业务监控指标
- 实现异常告警机制
- 完善日志记录

### 3. 扩展性增强
- 支持多环境配置
- 实现配置热更新
- 添加限流机制

## 📝 详细技术实现

### 1. 枚举转换系统

#### 1.1 婚姻状态转换
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/enums/HXBKMarriage.java" mode="EXCERPT">
````java
public enum HXBKMarriage {
    UNMARRIED("01", "未婚", Marriage.UNMARRIED),
    MARRIED("02", "已婚", Marriage.MARRIED),
    DIVORCED("99", "离异", Marriage.DIVORCED),
    WIDOWED("99", "丧偶", Marriage.WIDOWED),
    UNKNOWN("99", "未知", Marriage.UNKNOWN);

    public static String convertMarriage(Marriage marriage) {
        return Arrays.stream(values())
            .filter(hxbkMarriage -> hxbkMarriage.marriage == marriage)
            .findFirst()
            .map(HXBKMarriage::getCode)
            .orElse(UNKNOWN.getCode());
    }
}
````
</augment_code_snippet>

#### 1.2 学历转换
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/enums/HXBKEducation.java" mode="EXCERPT">
````java
public enum HXBKEducation {
    MASTER("10", "研究生或以上", Education.MASTER),
    DOCTOR("10", "研究生或以上", Education.DOCTOR),
    COLLEGE("20", "本科", Education.COLLEGE),
    JUNIOR_COLLEGE("30", "大专", Education.JUNIOR_COLLEGE),
    HIGH_SCHOOL("60", "高中", Education.HIGH_SCHOOL),
    PRIMARY_SCHOOL("40", "中专", Education.PRIMARY_SCHOOL),
    JUNIOR_HIGH_SCHOOL("99", "初中及以下", Education.JUNIOR_HIGH_SCHOOL),
    UNKNOWN("99", "未知", Education.UNKNOWN);
}
````
</augment_code_snippet>

### 2. 方法路由系统

#### 2.1 HXBK方法枚举
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/enums/HXBKMethod.java" mode="EXCERPT">
````java
public enum HXBKMethod {
    // 核心业务接口
    CREDIT_APPLY("riskplus.dubbridge.credit.apply", "1.0"),
    CREDIT_STATUS_QUERY("riskplus.dubbridge.credit.status.query", "1.0"),
    LOAN_APPLY("riskplus.dubbridge.usecredit.apply", "1.0"),
    LOAN_QUERY("riskplus.dubbridge.usecredit.status.query", "1.0"),
    REPAY_TRIAL("riskplus.dubbridge.repay.trial.count", "1.0"),
    REPAY_APPLY("riskplus.dubbridge.repay.withhold.repay", "1.0"),
    REPAY_QUERY("riskplus.dubbridge.repay.result.query", "1.0"),

    // 结清证明接口
    SETTLEMENT_CERTIFICATE_QUERY("riskplus.dubbridge.settlement.certificate.query", "1.0"),

    // 回调接口
    CREDIT_APPLY_RESULT_NOTIFY("dubhe.credit.apply.result.notify", "1.0"),
    LOAN_RESULT_CALLBACK("dubhe.callback.loan.result", "1.0"),
    REPAY_RESULT_CALLBACK_V2("dubhe.callback.repay.result.v2", "2.0");
}
````
</augment_code_snippet>

### 3. 对账处理系统

#### 3.1 对账服务架构
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/service/HXBKReccService.java" mode="EXCERPT">
````java
@Service
public class HXBKReccService implements BankReccService, InitializingBean {

    @Autowired
    private List<HXBKReccHandler> reccHandlers;
    private Map<ReccType, HXBKReccHandler> reccHandlerMap;

    @Autowired
    private List<HXBKReccQueryHandler> reccQueryHandlers;
    private Map<ReccType, HXBKReccQueryHandler> reccQueryHandlerMap;

    @Override
    public void process(ReccApplyVo apply) {
        logger.info("开始处理 HXBK 对账. reccDay: [{}], type: [{}]",
                   apply.getReccDay(), apply.getReccType());
        reccHandlerMap.get(apply.getReccType()).process(apply.getReccDay());
    }

    @Override
    public void afterPropertiesSet() {
        // 初始化处理器映射
        reccHandlerMap = reccHandlers.stream()
            .collect(Collectors.toMap(HXBKReccHandler::getReccType, Function.identity()));
        reccQueryHandlerMap = reccQueryHandlers.stream()
            .collect(Collectors.toMap(HXBKReccQueryHandler::getReccType, Function.identity()));
    }
}
````
</augment_code_snippet>

### 4. 文件类型映射系统

#### 4.1 影像件类型枚举
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/enums/HXBKFileType.java" mode="EXCERPT">
````java
public enum HXBKFileType {
    // 身份证件
    ID_FACE("1", "身份证正面（人像）", "idCardFront", FileType.ID_HEAD),
    ID_NATION("2", "身份证反面（国徽）", "idCardBack", FileType.ID_NATION),

    // 人脸识别
    CREDIT_FACE_REC("3", "授信环节人脸照", "faceRecognition", FileType.ID_FACE),

    // 协议文件
    WITHDRAW_PROTOCOL("11", "委托扣款授权书", "withdrawProtocal", FileType.ENTRUSTED_DEDUCTION_LETTER),
    SYNTHESIS_AUTHORIZATION("21", "综合授权书", "credit-mix-protocol", FileType.SYNTHESIS_AUTHORIZATION),

    // 承诺函
    NON_STUDENTS_DECLARE("74", "非在校学生承诺函", "non-students-declaration", FileType.PROMISE_NOT_STUDENT),
    DEBET_PURPOSE_DECLARE("75", "个人放款用途承诺书", "debet-purpose-declaration", FileType.PERSONAL_LOAN_USE_COMMITMENT);

    public static HXBKFileType getByFileType(FileType fileType) {
        return Arrays.stream(values())
            .filter(hxbkFileType -> hxbkFileType.fileType == fileType)
            .findFirst()
            .orElse(null);
    }
}
````
</augment_code_snippet>

## 🔍 关键业务逻辑详解

### 1. 授信申请核心逻辑

#### 1.1 数据构建过程
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/util/HXBKDataBuilder.java" mode="EXCERPT">
````java
public class HXBKDataBuilder {

    public static PersonalInfo buildPersonalInfo(Credit credit, Account account) {
        PersonalInfo personalInfo = new PersonalInfo();
        personalInfo.setName(account.getName());
        personalInfo.setIdNo(account.getCertNo());
        personalInfo.setMobile(account.getMobile());
        personalInfo.setEmail(account.getEmail());

        // 婚姻状态转换
        personalInfo.setMarriage(HXBKMarriage.convertMarriage(account.getMarriage()));

        // 学历转换
        personalInfo.setEducation(HXBKEducation.convertEducation(account.getEducation()));

        return personalInfo;
    }

    public static JobInfo buildJobInfo(Account account) {
        JobInfo jobInfo = new JobInfo();
        jobInfo.setCompanyName(account.getCompanyName());
        jobInfo.setCompanyAddress(account.getCompanyAddress());
        jobInfo.setCompanyPhone(account.getCompanyPhone());
        jobInfo.setIncome(account.getMonthlyIncome());

        return jobInfo;
    }
}
````
</augment_code_snippet>

### 2. 影像件上传核心逻辑

#### 2.1 文件分类和处理
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/service/HXBKImageFileService.java" mode="EXCERPT">
````java
public class HXBKImageFileService {

    public void uploadImageFilesToAntSftp(Credit credit, Account account, List<LoanFile> imageFileList) {
        try {
            // 1. 分类处理文件
            List<LoanFile> idCardFiles = imageFileList.stream()
                .filter(file -> file.getFileType() == FileType.ID_HEAD || file.getFileType() == FileType.ID_NATION)
                .collect(Collectors.toList());

            List<LoanFile> faceFiles = imageFileList.stream()
                .filter(file -> file.getFileType() == FileType.ID_FACE)
                .collect(Collectors.toList());

            // 2. 上传身份证文件
            if (!idCardFiles.isEmpty()) {
                uploadIdCardFiles(credit, account, idCardFiles);
            }

            // 3. 上传人脸文件
            if (!faceFiles.isEmpty()) {
                uploadFaceFiles(credit, account, faceFiles);
            }

        } catch (Exception e) {
            logger.error("上传影像件到蚂蚁SFTP失败", e);
            throw new BizException(BizErrorCode.FILE_UPLOAD_FAIL);
        }
    }

    private void uploadIdCardFiles(Credit credit, Account account, List<LoanFile> idCardFiles) {
        String batchNumber = generateBatchNumber();
        String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String remoteDir = hxbkConfig.getIdCardDir() + "/" + currentDate;
        String zipFileName = "IDCARD-" + batchNumber + ".zip";

        // 创建ZIP文件并上传
        createAndUploadZipFile(idCardFiles, remoteDir, zipFileName);

        // 创建索引文件
        createAndUploadIndexFile(credit, account, idCardFiles, remoteDir, zipFileName);

        // 记录上传日志
        logFileUpload(credit, zipFileName, remoteDir + "/" + zipFileName);
    }
}
````
</augment_code_snippet>

### 3. 结清证明处理逻辑

#### 3.1 证明状态处理
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/service/HXBKLoanFileService.java" mode="EXCERPT">
````java
public void processCreditSettleVoucherQuery(DownloadFileLog downloadFileLog) {
    try {
        // 构建查询请求
        HXBKSettlementCertificateRequest request = buildSettlementRequest(downloadFileLog);

        // 调用结清证明查询接口
        HXBKSettlementCertificateResponse response = hxbkRequestService.settlementCertificateQuery(request);

        if (response == null || CollectionUtils.isEmpty(response.getCertificateInfoList())) {
            logger.warn("结清证明查询响应为空, loanId: {}", downloadFileLog.getLoanId());
            return;
        }

        // 处理证明信息
        for (CertificateInfo certInfo : response.getCertificateInfoList()) {
            processCertificateInfo(downloadFileLog, certInfo);
        }

    } catch (Exception e) {
        logger.error("处理结清证明查询异常, loanId: {}", downloadFileLog.getLoanId(), e);
        updateDownloadFileLogStatus(downloadFileLog, DownloadStatus.FAIL, e.getMessage());
    }
}

private void processCertificateInfo(DownloadFileLog downloadFileLog, CertificateInfo certInfo) {
    switch (certInfo.getStatus()) {
        case "0" -> {
            // 有结清证明，下载文件
            downloadAndSaveCertificate(downloadFileLog, certInfo);
            updateDownloadFileLogStatus(downloadFileLog, DownloadStatus.SUCCESS, "结清证明下载成功");
        }
        case "1" -> {
            // 无结清证明
            updateDownloadFileLogStatus(downloadFileLog, DownloadStatus.NO_FILE, "无结清证明");
        }
        case "2" -> {
            // 开具中，等待下次查询
            logger.info("结清证明开具中，等待下次查询, loanId: {}", downloadFileLog.getLoanId());
        }
        case "3" -> {
            // 暂不支持
            updateDownloadFileLogStatus(downloadFileLog, DownloadStatus.NOT_SUPPORT, "暂不支持开具结清证明");
        }
        default -> {
            logger.warn("未知的结清证明状态: {}, loanId: {}", certInfo.getStatus(), downloadFileLog.getLoanId());
        }
    }
}
````
</augment_code_snippet>

## 🛠️ 工具类详细实现

### 1. 加密工具类

#### 1.1 RSA密钥对生成器
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/util/crypto/RSA2048KeyPairGenerator.java" mode="EXCERPT">
````java
public class RSA2048KeyPairGenerator {

    public static KeyPair generateKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048);
        return keyPairGenerator.generateKeyPair();
    }

    public static String getPublicKeyString(PublicKey publicKey) {
        byte[] encoded = publicKey.getEncoded();
        return Base64.getEncoder().encodeToString(encoded);
    }

    public static String getPrivateKeyString(PrivateKey privateKey) {
        byte[] encoded = privateKey.getEncoded();
        return Base64.getEncoder().encodeToString(encoded);
    }

    public static void generateAndPrintKeyPair() throws NoSuchAlgorithmException {
        KeyPair keyPair = generateKeyPair();

        String publicKeyString = getPublicKeyString(keyPair.getPublic());
        String privateKeyString = getPrivateKeyString(keyPair.getPrivate());

        System.out.println("=== RSA 2048位密钥对 ===");
        System.out.println("公钥:");
        System.out.println(publicKeyString);
        System.out.println("\n私钥:");
        System.out.println(privateKeyString);
    }
}
````
</augment_code_snippet>

#### 1.2 参数处理工具
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/util/crypto/ParamUtils.java" mode="EXCERPT">
````java
public class ParamUtils {

    public static TreeMap<String, String> parseParamFromRequest(Map<String, Object> requestJson) {
        TreeMap<String, String> params = new TreeMap<>();

        for (Map.Entry<String, Object> entry : requestJson.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 跳过sign字段
            if (!"sign".equals(key) && value != null) {
                params.put(key, value.toString());
            }
        }

        return params;
    }

    public static String buildSignString(TreeMap<String, String> params) {
        return params.entrySet().stream()
            .map(entry -> entry.getKey() + "=" + entry.getValue())
            .collect(Collectors.joining("&"));
    }

    public static Map<String, Object> buildResponseParams(String data, String secretKey, String requestId) {
        Map<String, Object> params = new TreeMap<>();
        params.put("data", data);
        params.put("secretKey", secretKey);
        params.put("requestId", requestId);
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));

        return params;
    }
}
````
</augment_code_snippet>

### 2. 批次号生成工具

#### 2.1 HXBK批次号规则
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/util/HXBKBatchNumberGenerator.java" mode="EXCERPT">
````java
public class HXBKBatchNumberGenerator {

    /**
     * 生成HXBK批次号
     * 格式: CJ + 年月日时分秒 + 四位随机数字
     * 示例: CJ202507111111111234
     */
    public static String generateBatchNumber() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        String timeStr = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        // 生成四位随机数字
        Random random = new Random();
        int randomNum = random.nextInt(9000) + 1000; // 1000-9999

        return "CJ" + timeStr + randomNum;
    }

    /**
     * 验证批次号格式
     */
    public static boolean isValidBatchNumber(String batchNumber) {
        if (StringUtils.isBlank(batchNumber)) {
            return false;
        }

        // 检查长度: CJ(2) + 时间(14) + 随机数(4) = 20
        if (batchNumber.length() != 20) {
            return false;
        }

        // 检查前缀
        if (!batchNumber.startsWith("CJ")) {
            return false;
        }

        // 检查时间部分是否为数字
        String timePart = batchNumber.substring(2, 16);
        String randomPart = batchNumber.substring(16);

        return timePart.matches("\\d{14}") && randomPart.matches("\\d{4}");
    }
}
````
</augment_code_snippet>

## 📋 测试用例和验证

### 1. 单元测试示例

#### 1.1 授信服务测试
<augment_code_snippet path="capital-core/src/test/java/com/jinghang/capital/core/banks/hxbk/service/HXBKCreditServiceTest.java" mode="EXCERPT">
````java
@ExtendWith(MockitoExtension.class)
class HXBKCreditServiceTest {

    @Mock
    private HXBKRequestService hxbkRequestService;

    @Mock
    private CommonService commonService;

    @InjectMocks
    private HXBKCreditService hxbkCreditService;

    @Test
    void testCreditApply_Success() {
        // Given
        Credit credit = createTestCredit();
        Account account = createTestAccount();
        List<LoanFile> materials = createTestMaterials();

        HXBKCreditApplyResponse response = new HXBKCreditApplyResponse();
        response.setResultCode("OK");
        response.setCustomNo("CUST123456");

        when(commonService.findCreditById(anyString())).thenReturn(credit);
        when(commonService.findAccountById(anyString())).thenReturn(account);
        when(hxbkRequestService.creditApply(any())).thenReturn(response);

        // When
        hxbkCreditService.contractUpload(credit.getId());

        // Then
        verify(hxbkRequestService).creditApply(any(HXBKCreditApplyRequest.class));
        verify(commonService).getHXBKCreditFlowRepository();
    }

    @Test
    void testCreditApply_Failure() {
        // Given
        Credit credit = createTestCredit();

        when(commonService.findCreditById(anyString())).thenReturn(credit);
        when(hxbkRequestService.creditApply(any())).thenThrow(new RuntimeException("网络异常"));

        // When & Then
        assertDoesNotThrow(() -> hxbkCreditService.contractUpload(credit.getId()));

        // 验证异常处理
        assertEquals(CreditStatus.FAIL, credit.getCreditStatus());
    }
}
````
</augment_code_snippet>

### 2. 集成测试

#### 2.1 加解密集成测试
<augment_code_snippet path="capital-core/src/test/java/com/jinghang/capital/core/banks/hxbk/util/crypto/CryptoIntegrationTest.java" mode="EXCERPT">
````java
@SpringBootTest
class CryptoIntegrationTest {

    @Autowired
    private HXBKCryptoUtil cryptoUtil;

    @Test
    void testEncryptDecryptFlow() throws Exception {
        // 测试数据
        String originalData = "{\"name\":\"张三\",\"idNo\":\"123456789012345678\"}";

        // 1. 加密流程测试
        String secretKey = UUID.randomUUID().toString().replace("-", "");
        String encryptedData = AESUtils.encryptDataWithAES(originalData, secretKey);
        String encryptedSecretKey = RSAUtils.encryptSecretKeyWithRSA(secretKey, cryptoUtil.getAntPublicKey());

        // 2. 构建请求参数
        Map<String, Object> requestParams = new TreeMap<>();
        requestParams.put("data", encryptedData);
        requestParams.put("secretKey", encryptedSecretKey);
        requestParams.put("method", "test.method");
        requestParams.put("requestId", "REQ123456");
        requestParams.put("timestamp", String.valueOf(System.currentTimeMillis()));

        // 3. 签名
        String signString = ParamUtils.buildSignString((TreeMap<String, String>) requestParams);
        String signature = RSAUtils.signData(signString, cryptoUtil.getPartnerPrivateKey());
        requestParams.put("sign", signature);

        // 4. 解密验证流程
        // 验签
        TreeMap<String, String> paramsToVerify = ParamUtils.parseParamFromRequest(requestParams);
        String toVerifyStr = ParamUtils.buildSignString(paramsToVerify);
        boolean verified = RSAUtils.verifyData(signature, toVerifyStr, cryptoUtil.getPartnerPublicKey());
        assertTrue(verified, "验签应该成功");

        // 解密SecretKey
        String decryptedSecretKey = RSAUtils.decryptSecretKeyWithRSA(encryptedSecretKey, cryptoUtil.getPartnerPrivateKey());
        assertEquals(secretKey, decryptedSecretKey, "解密的SecretKey应该与原始SecretKey一致");

        // 解密业务数据
        String decryptedData = AESUtils.decryptDataWithAES(encryptedData, decryptedSecretKey);
        assertEquals(originalData, decryptedData, "解密的业务数据应该与原始数据一致");
    }
}
````
</augment_code_snippet>

---

**文档编写**: 任栋晖
**最后更新**: 2025年7月14日
**版本**: v1.0
