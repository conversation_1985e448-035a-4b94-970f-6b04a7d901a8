# HXBK JSON序列化字段名问题修复文档

## 问题描述

在HXBK用信申请的JSON序列化过程中，发现报文中同时出现了`m_type`和`mtype`两个字段，而预期应该只有`m_type`字段。

### 问题报文示例
```
"materials.1.m_type":"2","materials.1.big_code":"20","fund_code":"D20250701000000001","materials.2.m_type":"2","materials.3.m_type":"2","materials.2.mtype":"2","borrower_emp_info.company_address":"南京市鼓楼区中山路321
```

可以看到同时出现了：
- `materials.2.m_type":"2"` (正确)
- `materials.2.mtype":"2"` (错误)

## 问题根源分析

### 1. 真正的问题根源
经过深入分析发现，问题出现在`HXBKLoanApplyRequest.java`中存在一个**多余的内部类Material**：

- **第101行**：`private HXBKMaterial[] materials;` - 实际使用的是外部的`HXBKMaterial`类
- **第322行**：`public static class Material` - 多余的内部类，从未被使用

### 2. 内部类的问题
多余的内部类`Material`中存在错误的getter/setter方法命名：

```java
public static class Material{
    @JsonProperty("m_type")
    private String mType;

    // 错误的方法命名
    public String getmType() {  // 应该是getMType()
        return mType;
    }

    public void setmType(String mType) {  // 应该是setMType()
        this.mType = mType;
    }
}
```

### 3. 问题原理
- 虽然实际使用的是`HXBKMaterial`类，但内部类`Material`的存在可能导致JSON序列化器的混淆
- 错误的getter方法名`getmType()`被解析为属性名`mtype`
- 这导致JSON输出中同时出现：
  - `m_type`：来自正确的`HXBKMaterial`类
  - `mtype`：来自错误的内部类`Material`

## 解决方案

### 最佳修复方法
**删除多余的内部类Material**，因为：
1. 该内部类从未被使用
2. 实际使用的是外部的`HXBKMaterial`类
3. 内部类的存在只会造成混淆和问题

### 修复位置
文件：`capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/dto/loan/HXBKLoanApplyRequest.java`
行数：322-389（整个内部类Material已被删除）

## 修复验证

修复后，JSON序列化将只输出正确的`m_type`字段，不再出现多余的`mtype`字段。

## 实际使用的类

项目中实际使用的是`HXBKMaterial`类，该类的定义是正确的：

```java
// HXBKMaterial.java - 正确的实现
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKMaterial {
    @JsonProperty("m_type")
    private String mType;

    // 正确的getter/setter方法
    public String getMType() {
        return mType;
    }

    public void setMType(String mType) {
        this.mType = mType;
    }
}
```

## 经验总结

1. **避免重复定义**：
   - 不要在同一个项目中创建功能相同的重复类
   - 如果需要内部类，确保其有明确的用途和正确的实现

2. **JavaBean命名规范**：
   - getter方法：`get` + 首字母大写的属性名
   - setter方法：`set` + 首字母大写的属性名

3. **JSON序列化最佳实践**：
   - 使用`@JsonProperty`注解明确指定JSON字段名
   - 确保getter/setter方法命名符合JavaBean规范
   - 避免同一个属性产生多个JSON字段

4. **代码审查要点**：
   - 检查是否存在未使用的类或方法
   - 确保JSON序列化相关的注解和方法命名正确
   - 避免在DTO类中定义多余的内部类

## 相关文件

- `HXBKLoanApplyRequest.java` - 删除了多余的Material内部类
- `HXBKMaterial.java` - 实际使用的正确实现，可作为参考

## 修复日期
2025-07-28
