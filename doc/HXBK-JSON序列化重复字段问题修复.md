# HXBK JSON序列化重复字段问题修复文档

## 问题描述

在HXBK用信申请的JSON序列化过程中，发现报文中同时出现了`m_type`和`mtype`两个字段，而预期应该只有`m_type`字段。

### 问题报文示例
```json
{
  "materials.1.m_type":"2",
  "materials.1.big_code":"20", 
  "materials.2.m_type":"2",
  "materials.2.mtype":"2",
  "materials.3.m_type":"2",
  "materials.3.mtype":"2"
}
```

可以看到同时出现了：
- `materials.2.m_type":"2"` (正确)
- `materials.2.mtype":"2"` (错误)

## 问题根源分析

### 真正的问题根源
经过深入分析发现，问题出现在`HXBKMaterial.java`类中的Jackson序列化配置：

```java
public class HXBKMaterial {
    @JsonProperty("m_type")
    private String mType;
    
    // 问题：getter方法被Jackson自动识别
    public String getMType() {  // 被识别为属性名 "mtype"
        return mType;
    }
}
```

### 问题原理
1. **@JsonProperty注解**：`@JsonProperty("m_type")` 告诉Jackson将字段序列化为 `m_type`
2. **JavaBean自动识别**：`getMType()` 方法被Jackson按照JavaBean命名规范自动识别为属性名 `mtype`
3. **重复序列化**：Jackson同时处理了注解指定的字段名和自动识别的属性名，导致同一个字段被序列化两次

### Jackson序列化机制
- Jackson默认会扫描所有public getter方法
- 根据JavaBean命名规范，`getMType()` 对应属性名 `mtype`（首字母小写）
- 当同时存在`@JsonProperty`注解和getter方法时，Jackson会产生两个JSON字段

## 解决方案

### 修复方法
在getter方法上添加`@JsonIgnore`注解，防止Jackson自动识别该方法：

```java
public class HXBKMaterial {
    @JsonProperty("m_type")
    private String mType;
    
    @JsonIgnore  // 防止Jackson自动识别此getter方法
    public String getMType() {
        return mType;
    }
    
    public void setMType(String mType) {
        this.mType = mType;
    }
}
```

### 修复位置
文件：`capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/dto/credit/HXBKMaterial.java`
- 第3行：添加 `import com.fasterxml.jackson.annotation.JsonIgnore;`
- 第66行：在 `getMType()` 方法上添加 `@JsonIgnore` 注解

## 修复验证

修复后，JSON序列化将只输出正确的`m_type`字段，不再出现多余的`mtype`字段。

### 修复前
```json
{
  "materials.1.m_type":"2",
  "materials.1.mtype":"2"  // 错误的重复字段
}
```

### 修复后
```json
{
  "materials.1.m_type":"2"  // 只有正确的字段
}
```

## 技术原理

### Jackson序列化优先级
1. **字段级注解**：`@JsonProperty` 直接作用在字段上
2. **方法级注解**：`@JsonProperty` 作用在getter/setter方法上  
3. **自动发现**：根据JavaBean命名规范自动识别getter方法

### 最佳实践
1. **使用@JsonProperty注解**：明确指定JSON字段名
2. **添加@JsonIgnore注解**：防止不需要的getter方法被自动识别
3. **保持一致性**：同一个属性只使用一种序列化方式

## 经验总结

### 1. JSON序列化最佳实践
- 使用`@JsonProperty`注解明确指定JSON字段名
- 对不需要序列化的getter方法添加`@JsonIgnore`注解
- 避免同一个属性产生多个JSON字段

### 2. JavaBean命名规范
- getter方法：`get` + 首字母大写的属性名
- setter方法：`set` + 首字母大写的属性名
- Jackson会根据此规范自动识别属性

### 3. 代码审查要点
- 检查DTO类中是否存在重复的JSON字段
- 确保JSON序列化相关的注解使用正确
- 验证getter/setter方法命名符合JavaBean规范

### 4. 调试技巧
- 使用Jackson的`ObjectMapper.writeValueAsString()`方法测试序列化结果
- 检查生成的JSON中是否有重复字段
- 使用`@JsonIgnore`或`@JsonProperty`明确控制序列化行为

## 相关文件

- `HXBKMaterial.java` - 修复了重复字段问题的核心类
- `HXBKLoanApplyRequest.java` - 使用HXBKMaterial的请求类

## 修复日期
2025-07-29

## 影响范围
- HXBK用信申请接口
- 所有使用HXBKMaterial类的JSON序列化场景
