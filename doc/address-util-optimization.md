# AddressUtil 地址解析优化文档

## 问题描述

用户反馈 `AddressUtil.addressResolution()` 方法对以下两个地址解析不正确：

1. `广东省东莞市长安镇振安东路` 
   - 问题：`area='未知区域', detail='长安镇振安东路'`
   - 期望：`area='长安镇', detail='振安东路'`

2. `安徽省诸头市龙潭乡妫栋7号80号院`
   - 问题：`area='未知区域', detail='龙潭乡妫栋7号80号院'`
   - 期望：`area='龙潭乡', detail='妫栋7号80号院'`

## 问题分析

原始正则表达式中的 `area` 匹配组没有包含"镇"和"乡"等行政单位：

```java
// 原始正则表达式
String regex = "(?<province>[^省市区县]+?(省|自治区|特别行政区|市)|上海市|北京市|天津市|重庆市)(?<city>市辖区|([^省市区县]+?(市|自治州|地区|盟|区划|行政单位)))?(?<area>([市]?辖区|开发区|[^省市县]+?(县|区|市|旗|岛|自治县|自治旗|林区|特区)))?(?<detail>.*)";
```

问题在于：
- `area` 组中缺少对"镇"、"乡"等行政单位的支持
- 字符类 `[^省市区县]` 和 `[^省市县]` 没有排除"镇"、"乡"字符

## 解决方案

### 1. 优化正则表达式

更新正则表达式，增加对更多行政单位的支持：

```java
// 优化后的正则表达式
String regex = "(?<province>[^省市区县镇乡]+?(省|自治区|特别行政区|市)|上海市|北京市|天津市|重庆市)(?<city>市辖区|([^省市区县镇乡]+?(市|自治州|地区|盟|区划|行政单位)))?(?<area>([市]?辖区|开发区|[^省市县镇乡]+?(县|区|市|旗|岛|自治县|自治旗|林区|特区|镇|乡|街道|办事处|苏木|民族乡|民族苏木)))?(?<detail>.*)";
```

### 2. 主要改进点

1. **字符类优化**：
   - `[^省市区县]` → `[^省市区县镇乡]`
   - `[^省市县]` → `[^省市县镇乡]`

2. **行政单位扩展**：
   - 原有：`县|区|市|旗|岛|自治县|自治旗|林区|特区`
   - 新增：`镇|乡|街道|办事处|苏木|民族乡|民族苏木`

## 测试结果

### 问题地址测试
```
测试地址1: 广东省东莞市长安镇振安东路
AddressInfo{province='广东省', city='东莞市', area='长安镇', detail='振安东路'}

测试地址2: 安徽省诸头市龙潭乡妫栋7号80号院
AddressInfo{province='安徽省', city='诸头市', area='龙潭乡', detail='妫栋7号80号院'}
```

### 其他测试用例
```
=== 镇乡街道办事处情况 ===
河北省石家庄市正定县正定镇常山西路 → province='河北省', city='石家庄市', area='正定县', detail='正定镇常山西路'
山西省太原市小店区坞城街道学府街 → province='山西省', city='太原市', area='小店区', detail='坞城街道学府街'
湖南省长沙市岳麓区桔子洲街道潇湘中路 → province='湖南省', city='长沙市', area='岳麓区', detail='桔子洲街道潇湘中路'
四川省成都市武侯区跳伞塔街道人民南路 → province='四川省', city='成都市', area='武侯区', detail='跳伞塔街道人民南路'
内蒙古自治区呼和浩特市土默特左旗毕克齐镇 → province='内蒙古自治区', city='呼和浩特市', area='土默特左旗', detail='毕克齐镇'
新疆维吾尔自治区乌鲁木齐市达坂城区达坂城镇 → province='新疆维吾尔自治区', city='乌鲁木齐市', area='达坂城区', detail='达坂城镇'
```

## 支持的行政单位类型

优化后的方法现在支持以下行政单位：

### 省级
- 省、自治区、特别行政区、直辖市

### 市级  
- 市、自治州、地区、盟

### 县级
- 县、区、市、旗、岛、自治县、自治旗、林区、特区

### 乡镇级（新增）
- 镇、乡、街道、办事处、苏木、民族乡、民族苏木

## 文件位置

`capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/util/AddressUtil.java`

## 优化时间

2025-07-28

## 影响范围

此优化主要影响 HXBK 银行相关的地址解析功能，提高了地址解析的准确性，特别是对镇、乡等基层行政单位的识别能力。
