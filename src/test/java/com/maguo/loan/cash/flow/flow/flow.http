### 限流测试（POST /test/flow）
POST {{domain}}/test/flow
Content-Type: application/json

{
    "userId": "userId_cb2fe25eef22",
    "partnerUserId": "partnerUserId_b4011c64a0d7",
    "phone": "phone_1810e60930d2",
    "name": "name_a04317f569ae",
    "idCard": "idCard_ac1cb8ca5d57",
    "bankCardNo": "bankCardNo_46da3d5d6543",
    "creditAmount": 0.00,
    "applyPeriod": "applyPeriod_dbe764af9dcc",
    "centerLoanUser": 0,
    "authInfo": {
        "frontUrl": "frontUrl_bb4accf1514c",
        "backUrl": "backUrl_4e0afa869dce",
        "borrower": "borrower_4f3a2e187df0",
        "address": "address_813fcc7cc022",
        "startDueTimeOcr": "startDueTimeOcr_5dbd72f89f34",
        "endDueTimeOcr": "endDueTimeOcr_1196509748ab",
        "sex": 0,
        "birthday": "birthday_d3dfffea7b93",
        "nation": "nation_2561d8ec06a9",
        "authority": "authority_d2b777ac17aa",
        "liveRate": "liveRate_c0e3db36bf79",
        "facialSupplier": "facialSupplier_9da411c32e4c"
    },
    "baseInfo": {
        "maritalStatus": "maritalStatus_43e72fdb1e18",
        "educational": "educational_c4e63ccd00ed",
        "companyAddress": "companyAddress_6e1ef3afaa15",
        "companyName": "companyName_2d60506bd347",
        "companyPhone": "companyPhone_af0fdedef285",
        "companyProvince": "companyProvince_1c0c80298436",
        "companyCity": "companyCity_b6238b367e2c",
        "companyArea": "companyArea_2ea928599dfb",
        "companyProvinceCode": "companyProvinceCode_072a208bbd75",
        "companyCityCode": "companyCityCode_71ede30bc87b",
        "companyAreaCode": "companyAreaCode_1d2927fb35a1",
        "inCome": "inCome_e57395ec49d4",
        "liveAddress": "liveAddress_dd5c4d6af11e",
        "liveProvince": "liveProvince_7f45a84a0ae0",
        "liveCity": "liveCity_c4aa3d70438c",
        "liveArea": "liveArea_5cf3f0c162e4",
        "provinceCode": "provinceCode_a3486e64ec0f",
        "cityCode": "cityCode_04fe2f963f1d",
        "areaCode": "areaCode_a86ac32bfc3c",
        "workType": "workType_7c0b1d8c424a",
        "industry": "industry_a0c0bc4a52d4",
        "workingYears": "workingYears_ba5d9ad97af1",
        "department": "department_38cbc391db2d",
        "companyType": "companyType_b0c928cf70d8",
        "position": "position_e2edcb9d7cbd",
        "loanPurpose": "loanPurpose_70a2f7ab5072",
        "houseType": "houseType_ff1e8b21e51c",
        "email": "email_4e348d23c67c",
        "channelLabel": 0
    },
    "deviceInfo": {
        "gpsLng": "gpsLng_ecfa853b5f18",
        "gpsLat": "gpsLat_468e50d7d95e",
        "gpsAddress": "gpsAddress_05f2c683dc14",
        "gpsProvince": "gpsProvince_9f23ca9f41c3",
        "gpsCity": "gpsCity_ef21345b8490",
        "osType": "osType_e07146b6398d",
        "userIP": "userIP_a85b778e53b7",
        "deviceId": "deviceId_4b1dfbd4525c",
        "simId": "simId_3d8b904b7539",
        "netStatus": "netStatus_ac8885eaca65",
        "systemVersion": "systemVersion_1da8095b2ac4",
        "deviceModel": "deviceModel_c37687959a96"
    },
    "contactInfos": [
        {
            "name": "name_5904288e4d90",
            "phone": "phone_727075f934e2",
            "relation": "relation_413c7e677e26"
        }
    ]
}

> {%
    client.test("Request executed successfully", function (){
        client.log("response status：" + response.status);
        client.assert(response.status === 200, "Response status is not 200");
        client.log("response body code：" + response.body.code);
    });
%}
