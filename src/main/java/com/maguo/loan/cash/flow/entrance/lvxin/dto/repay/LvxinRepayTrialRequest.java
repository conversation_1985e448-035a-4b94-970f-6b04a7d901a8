package com.maguo.loan.cash.flow.entrance.lvxin.dto.repay;

import java.util.List;

/**
 * @ClassName RepayTrialRequest
 * <AUTHOR>
 * @Description 还款试算
 * @Date 2024/5/24 14:37
 * @Version v1.0
 **/
public class LvxinRepayTrialRequest {
    /**
     * 绿信授信流水号
     */
    private String partnerUserId;
    /**
     * LoanId
     */
    private String partnerOrderNo;
    private String loanGid;
    /**
     * 还款期数集合[1,2,3]
     */
    private List<Integer> repayList;

    /**
     * 还款期数
     */
    private Integer period;
    /**
     * 当期 结清
     */
    private String repayType;

    //客户实际还款日期
    private String repayDate;

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public String getPartnerUserId() {
        return partnerUserId;
    }

    public void setPartnerUserId(String partnerUserId) {
        this.partnerUserId = partnerUserId;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getLoanGid() {
        return loanGid;
    }

    public void setLoanGid(String loanGid) {
        this.loanGid = loanGid;
    }

    public List<Integer> getRepayList() {
        return repayList;
    }

    public void setRepayList(List<Integer> repayList) {
        this.repayList = repayList;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }
}
