package com.maguo.loan.cash.flow.entity;


import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.WriteOffTypeEnum;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 线下还款申请
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "offline_repay_apply")
public class OfflineRepayApply extends BaseEntity {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 外部订单号
     */
    private String outerRepayNo;

    public String getOuterRepayNo() {
        return outerRepayNo;
    }

    public void setOuterRepayNo(String outerRepayNo) {
        this.outerRepayNo = outerRepayNo;
    }

    /**
     * 借据号
     */
    private String loanId;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 还款模式
     */
    @Enumerated(EnumType.STRING)
    private RepayPurpose repayPurpose;

    /**
     * 销账类型
     */
    @Enumerated(EnumType.STRING)
    private WriteOffTypeEnum writeOffType;

    /**
     /**
     * 申请时间
     */
    private LocalDateTime applyTime;
    /**
     * 应还本金
     */
    private BigDecimal principalAmt;
    /**
     * 应还利息
     */
    private BigDecimal interestAmt;
    /**
     * 应还担保费
     */
    private BigDecimal guaranteeAmt;
    /**
     * 应还罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 应还咨询费
     */
    private BigDecimal consultFee;
    /**
     * 应还总金额
     */
    private BigDecimal amount;
    /**
     * 减免金额
     */
    private BigDecimal reduceAmount;
    /**
     * 实还本金
     */
    private BigDecimal actPrincipalAmt;
    /**
     * 实还利息
     */
    private BigDecimal actInterestAmt;
    /**
     * 实还担保费
     */
    private BigDecimal actGuaranteeAmt;
    /**
     * 实还罚息
     */
    private BigDecimal actPenaltyAmt;
    /**
     * 实还咨询费
     */
    private BigDecimal actConsultFee;

    /**
     * 实还咨询费
     */
    private BigDecimal breachFee;

    public BigDecimal getBreachFee() {
        return breachFee;
    }

    public void setBreachFee(BigDecimal breachFee) {
        this.breachFee = breachFee;
    }

    /**
     * 实还总金额
     */
    private BigDecimal actAmount;
    /**
     * 销账状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState applyState;

    /**
     * 溢出金额
     */
    private BigDecimal overflowAmount;

    /**
     * 实还平台罚息（对客罚息 - 对资罚息）
     */
    private BigDecimal actPlatformPenaltyAmt;


    private LocalDateTime actTime;

    public LocalDateTime getActTime() {
        return actTime;
    }

    public void setActTime(LocalDateTime actTime) {
        this.actTime = actTime;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getConsultFee() {
        return consultFee;
    }

    public void setConsultFee(BigDecimal consultFee) {
        this.consultFee = consultFee;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getReduceAmount() {
        return reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    public BigDecimal getActPrincipalAmt() {
        return actPrincipalAmt;
    }

    public void setActPrincipalAmt(BigDecimal actPrincipalAmt) {
        this.actPrincipalAmt = actPrincipalAmt;
    }

    public BigDecimal getActInterestAmt() {
        return actInterestAmt;
    }

    public void setActInterestAmt(BigDecimal actInterestAmt) {
        this.actInterestAmt = actInterestAmt;
    }

    public BigDecimal getActGuaranteeAmt() {
        return actGuaranteeAmt;
    }

    public void setActGuaranteeAmt(BigDecimal actGuaranteeAmt) {
        this.actGuaranteeAmt = actGuaranteeAmt;
    }

    public BigDecimal getActPenaltyAmt() {
        return actPenaltyAmt;
    }

    public void setActPenaltyAmt(BigDecimal actPenaltyAmt) {
        this.actPenaltyAmt = actPenaltyAmt;
    }

    public BigDecimal getActConsultFee() {
        return actConsultFee;
    }

    public void setActConsultFee(BigDecimal actConsultFee) {
        this.actConsultFee = actConsultFee;
    }

    public BigDecimal getActAmount() {
        return actAmount;
    }

    public void setActAmount(BigDecimal actAmount) {
        this.actAmount = actAmount;
    }

    public ProcessState getApplyState() {
        return applyState;
    }

    public void setApplyState(ProcessState applyState) {
        this.applyState = applyState;
    }

    public WriteOffTypeEnum getWriteOffType() {
        return writeOffType;
    }

    public void setWriteOffType(WriteOffTypeEnum writeOffType) {
        this.writeOffType = writeOffType;
    }

    public BigDecimal getOverflowAmount() {
        return overflowAmount;
    }

    public void setOverflowAmount(BigDecimal overflowAmount) {
        this.overflowAmount = overflowAmount;
    }

    public BigDecimal getActPlatformPenaltyAmt() {
        return actPlatformPenaltyAmt;
    }

    public void setActPlatformPenaltyAmt(BigDecimal actPlatformPenaltyAmt) {
        this.actPlatformPenaltyAmt = actPlatformPenaltyAmt;
    }

    @Override
    protected String prefix() {
        return "ORA";
    }

    @Override
    public void prePersist() {
        if (StringUtil.isBlank(super.getId())) {
            setId(genId());
        }
        if (StringUtil.isBlank(super.getCreatedBy())) {
            super.setCreatedBy("sys");
        }
        if (super.getCreatedTime() == null) {
            super.setCreatedTime(LocalDateTime.now());
        }
        if (StringUtil.isBlank(super.getUpdatedBy())) {
            super.setUpdatedBy("sys");
        }
        super.setUpdatedTime(LocalDateTime.now());
    }
}
