package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.ReccState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "reconciliation_file")
public class ReconciliationFile extends BaseEntity {
    private String fileOrigin;
    /**
     * 文件类型
     */
    @Enumerated(EnumType.STRING)
    private FileType fileType;
    /**
     * 文件日期
     */
    private LocalDate fileDate;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * bucket
     */
    private String ossBucket;
    /**
     * key
     */
    private String ossPath;
    /**
     * 对账状态
     */
    @Enumerated(EnumType.STRING)
    private ReccState recState;

    public String getFileOrigin() {
        return fileOrigin;
    }

    public void setFileOrigin(String fileOrigin) {
        this.fileOrigin = fileOrigin;
    }



    public LocalDate getFileDate() {
        return fileDate;
    }

    public void setFileDate(LocalDate fileDate) {
        this.fileDate = fileDate;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOssBucket() {
        return ossBucket;
    }

    public void setOssBucket(String ossBucket) {
        this.ossBucket = ossBucket;
    }

    public String getOssPath() {
        return ossPath;
    }

    public void setOssPath(String ossPath) {
        this.ossPath = ossPath;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public ReccState getRecState() {
        return recState;
    }

    public void setRecState(ReccState recState) {
        this.recState = recState;
    }

    @Override
    protected String prefix() {
        return "RF";
    }
}
