package com.maguo.loan.cash.flow.entity;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanPurpose;
import com.maguo.loan.cash.flow.enums.ProcessState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "credit")
public class Credit extends BaseEntity {
    private String userId;

    /**
     * 路由id
     */
    private String routerRecordId;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;
    /**
     * 外部授信单号
     */
    private String outerCreditId;
    /**
     * 放款卡id
     */
    private String loanCardId;
    /**
     * 授信金额
     */
    private BigDecimal creditAmt;
    /**
     * 申请期数
     */
    private Integer periods;
    /**
     * 借款用途
     */
    @Enumerated(EnumType.STRING)
    private LoanPurpose loanPurpose;
    /**
     * 申请时间
     */
    private LocalDateTime applyTime;
    /**
     * 资金渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;
    /**
     * 融担公司
     */
    @Enumerated(EnumType.STRING)
    private GuaranteeCompany guaranteeCompany;
    /**
     * 资金编号
     */
    private String creditNo;
    /**
     * 授信状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState state;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 对客利率（金融）
     */
    private BigDecimal irrRate;
    /**
     * 额度合同
     */
    private String amountContractNo;

    private String loanContractNo;
    /**
     * 通过时间
     */
    private LocalDateTime passTime;
    /**
     * 有效期
     */
    private LocalDateTime capExpireTime;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRouterRecordId() {
        return routerRecordId;
    }

    public void setRouterRecordId(String routerRecordId) {
        this.routerRecordId = routerRecordId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }



    public String getOuterCreditId() {
        return outerCreditId;
    }

    public void setOuterCreditId(String outerCreditId) {
        this.outerCreditId = outerCreditId;
    }

    public String getLoanCardId() {
        return loanCardId;
    }

    public void setLoanCardId(String loanCardId) {
        this.loanCardId = loanCardId;
    }

    public BigDecimal getCreditAmt() {
        return creditAmt;
    }

    public void setCreditAmt(BigDecimal creditAmt) {
        this.creditAmt = creditAmt;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }


    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }


    public String getCreditNo() {
        return creditNo;
    }

    public void setCreditNo(String creditNo) {
        this.creditNo = creditNo;
    }


    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getAmountContractNo() {
        return amountContractNo;
    }

    public void setAmountContractNo(String amountContractNo) {
        this.amountContractNo = amountContractNo;
    }

    public String getLoanContractNo() {
        return loanContractNo;
    }

    public void setLoanContractNo(String loanContractNo) {
        this.loanContractNo = loanContractNo;
    }

    public LocalDateTime getPassTime() {
        return passTime;
    }

    public void setPassTime(LocalDateTime passTime) {
        this.passTime = passTime;
    }

    public LocalDateTime getCapExpireTime() {
        return capExpireTime;
    }

    public void setCapExpireTime(LocalDateTime capExpireTime) {
        this.capExpireTime = capExpireTime;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public LoanPurpose getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(LoanPurpose loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public ProcessState getState() {
        return state;
    }

    public void setState(ProcessState state) {
        this.state = state;
    }

    public BigDecimal getIrrRate() {
        return irrRate;
    }

    public void setIrrRate(BigDecimal irrRate) {
        this.irrRate = irrRate;
    }
    @Override
    protected String prefix() {
        return "CR";
    }

}
