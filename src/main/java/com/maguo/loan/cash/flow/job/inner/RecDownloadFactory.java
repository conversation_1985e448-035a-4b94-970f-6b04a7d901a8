package com.maguo.loan.cash.flow.job.inner;

import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.job.inner.service.AbstractRecDownloadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/9
 */
@Service
public class RecDownloadFactory {
    private List<AbstractRecDownloadService> recDownloadServices;

    public AbstractRecDownloadService getRecDownloadService(FileType reccType) {
        return recDownloadServices.stream().filter(s -> s.reccType().equals(reccType)).findFirst().orElseThrow();
    }

    @Autowired
    public void setRecDownloadServices(List<AbstractRecDownloadService> recDownloadServices) {
        this.recDownloadServices = recDownloadServices;
    }
}
