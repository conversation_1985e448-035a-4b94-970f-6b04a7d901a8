package com.maguo.loan.cash.flow.entrance.lvxin.config;

import com.maguo.loan.cash.flow.common.RequestUriLogFilter;
import com.maguo.loan.cash.flow.entrance.lvxin.filter.EncryptFilter;
import jakarta.servlet.Filter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.CompositeFilter;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @Description 绿信流量配置
* @Date 2025-05-17
* @Version 1.0
*/
@Configuration
public class LvxinFilterConfig {

    /**
     * 注入配置属性
     * @param lvxinFlowConfig
     * @return
     */
    @Bean
    public FilterRegistrationBean<CompositeFilter> lvxinFlowFilter(LvxinFlowConfig lvxinFlowConfig) {
        List<Filter> filterList = new ArrayList<>();
        filterList.add(new RequestUriLogFilter());
        filterList.add(new EncryptFilter(lvxinFlowConfig));
        CompositeFilter compositeFilter = new CompositeFilter();
        compositeFilter.setFilters(filterList);
        FilterRegistrationBean<CompositeFilter> bean = new FilterRegistrationBean<>(compositeFilter);
        // fixme 验证url
        bean.addUrlPatterns("/lvxin/*");
        return bean;
    }

}
