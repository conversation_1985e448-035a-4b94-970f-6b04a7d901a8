package com.maguo.loan.cash.flow.common.sentinel;

import com.alibaba.csp.sentinel.adapter.spring.webmvc.callback.BlockExceptionHandler;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.fastjson2.JSONObject;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.common.ResultCode;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.Part;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * Custom sentinel block exception handler.
 *
 * <AUTHOR>
 */

@Component
public class CustomSentinelBlockExceptionHandler implements BlockExceptionHandler {
    /**
     * Logger.
     */
    private static final Logger LOGGER = org.slf4j.LoggerFactory.getLogger(CustomSentinelBlockExceptionHandler.class);

    /**
     * Handle the {@link BlockException} when request is blocked.
     *
     * @param request  Servlet request. {@link jakarta.servlet.http.HttpServletRequest}
     * @param response Servlet response. {@link jakarta.servlet.http.HttpServletResponse}
     * @param e the block exception. {@link Exception}
     * @since 1.0.0
     */
    @Override
    public void handle(final HttpServletRequest request,
                       final HttpServletResponse response,
                       final BlockException e) throws Exception {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Blocked by Sentinel, url：{}，body：{} ", request.getRequestURL(), getRequestParams(request), e);
        }
        response.setStatus(HttpStatus.SC_OK);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        try (PrintWriter out = response.getWriter()) {
            JSONObject r = new JSONObject();
            r.put("code", ResultCode.TOO_MANY_REQUEST.getCode());
            r.put("message", ResultCode.TOO_MANY_REQUEST.getMsg());
            out.print(r.toJSONString());
            out.flush();
        } catch (IOException ex) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.error("Sending response failed, ", ex);
            }
        }
    }

    /**
     * 获取请求参数.
     *
     * @param request 请求.
     * @return 请求参数.
     */
    private String getRequestParams(final HttpServletRequest request) {
        if (isFileUploadRequest(request)) {
            return null;
        }

        // GET/POST/PUT等.
        String method = request.getMethod();
        if ("GET".equalsIgnoreCase(method)) {
            return request.getQueryString();
        } else {
            try(BufferedReader reader = request.getReader()) {
                StringBuilder body = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }
                return body.toString();
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * 判断请求是否为文件上传请求。
     *
     * @param request 请求.
     * @return 是否为文件上传请求。
     */
    private boolean isFileUploadRequest(final HttpServletRequest request) {
        // 1. 检查 Content-Type.
        String contentType = request.getContentType();
        if (StringUtil.isBlank(contentType) || !contentType.startsWith("multipart/form-data")) {
            return false;
        }

        // 2. 尝试解析（Servlet 3.0+）
        try {
            Part part = request.getPart("file");
            return part != null && part.getSize() > 0;
        } catch (IOException | ServletException e) {
            return false;
        }
    }
}
