package com.maguo.loan.cash.flow.service.listener;


import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.dto.OnlineRepayApplyRequest;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.enums.OperationSource;
import com.maguo.loan.cash.flow.enums.PaySide;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.enums.RepayType;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.RepayService;
import com.maguo.loan.cash.flow.service.WarningService;

import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> gale
 * @Classname BatchPreRepayFeeListener
 * @Description 提前结清费用扣款
 * @Date 2024/1/25 11:37
 */
@Component
public class BatchPreRepayFeeListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(BatchPreRepayFeeListener.class);

    @Autowired
    private RepayService repayService;

    @Autowired
    private RepayPlanRepository repayPlanRepository;


    public BatchPreRepayFeeListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.DUE_BATCH_PRE_REPAY_FEE)
    public void listenBatchRepayRepay(Message message, Channel channel) {
        String loaIdStr = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            Set loanIds = JsonUtil.convertToObject(loaIdStr, Set.class);
            logger.info("批量提前费用还款:{}", JsonUtil.toJsonString(loanIds));

            for (Object loanId : loanIds) {
                List<RepayPlan> repayPlans = repayPlanRepository.findByLoanIdAndCustRepayStateOrderByPeriodAsc(loanId.toString(), RepayState.NORMAL);
                if (CollectionUtil.isEmpty(repayPlans)) {
                    //批扣中已有用户还款
                    continue;
                }

                OnlineRepayApplyRequest onlineRepayApplyRequest = getOnlineRepayApplyRequest(repayPlans.get(0));
                try {
                    repayService.online(onlineRepayApplyRequest);
                } catch (Exception e) {
                    logger.error("批量提前费用还款异常:{},{}", loanId, e.getMessage());
                }
            }
        } catch (Exception e) {
            logger.error("批量提前费用还款异常:{},{}", loaIdStr, e.getMessage());
        } finally {
            ackMsg(loaIdStr, message, channel);
        }
    }


    private static OnlineRepayApplyRequest getOnlineRepayApplyRequest(RepayPlan plan) {
        OnlineRepayApplyRequest onlineRepayApplyRequest = new OnlineRepayApplyRequest();
        onlineRepayApplyRequest.setRepayMode(RepayMode.ONLINE);
        onlineRepayApplyRequest.setRepayType(RepayType.REPAY);
        onlineRepayApplyRequest.setPaySide(PaySide.CAPITAL);
        onlineRepayApplyRequest.setOperationSource(OperationSource.BATCH);
        onlineRepayApplyRequest.setPeriod(plan.getPeriod());
        onlineRepayApplyRequest.setRepayPurpose(RepayPurpose.CLEAR);
        onlineRepayApplyRequest.setLoanId(plan.getLoanId());
        return onlineRepayApplyRequest;
    }
}

