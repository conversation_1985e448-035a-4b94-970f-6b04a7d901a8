package com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.common;

import java.math.BigDecimal;

public class RepayContext {
    // 公共字段
    private String loanReqNo;
    private String sourceCode;
    private String repayNo;
    private String repayType;
    private String repayTime;
    private Integer repayTerm;
    private BigDecimal repayAmount;
    private BigDecimal repayPrincipal;
    private BigDecimal repayInterest;
    private BigDecimal repayOverdue;
    private BigDecimal repayPoundage;
    private BigDecimal repayLateFee;

    // Deduct特有字段
    private String bankCode;
    private String bankAcct;
    private String acctName;
    private String bankMobile;
    private String bankChannel;
    private String channelRepayId;
    private String mainMemberId;
    private String payOrderNo;

    // RepayNotice特有字段
    private String repayMode;
    private String applyReductNo;
    private BigDecimal reductAmount;
    private BigDecimal reductPrincipal;
    private BigDecimal reductInterest;
    private BigDecimal reductOverdue;
    private BigDecimal reductPoundage;

    public String getLoanReqNo() {
        return loanReqNo;
    }

    public void setLoanReqNo(String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getRepayNo() {
        return repayNo;
    }

    public void setRepayNo(String repayNo) {
        this.repayNo = repayNo;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public String getRepayTime() {
        return repayTime;
    }

    public void setRepayTime(String repayTime) {
        this.repayTime = repayTime;
    }

    public Integer getRepayTerm() {
        return repayTerm;
    }

    public void setRepayTerm(Integer repayTerm) {
        this.repayTerm = repayTerm;
    }

    public BigDecimal getRepayAmount() {
        return repayAmount;
    }

    public void setRepayAmount(BigDecimal repayAmount) {
        this.repayAmount = repayAmount;
    }

    public BigDecimal getRepayPrincipal() {
        return repayPrincipal;
    }

    public void setRepayPrincipal(BigDecimal repayPrincipal) {
        this.repayPrincipal = repayPrincipal;
    }

    public BigDecimal getRepayInterest() {
        return repayInterest;
    }

    public void setRepayInterest(BigDecimal repayInterest) {
        this.repayInterest = repayInterest;
    }

    public BigDecimal getRepayOverdue() {
        return repayOverdue;
    }

    public void setRepayOverdue(BigDecimal repayOverdue) {
        this.repayOverdue = repayOverdue;
    }

    public BigDecimal getRepayPoundage() {
        return repayPoundage;
    }

    public void setRepayPoundage(BigDecimal repayPoundage) {
        this.repayPoundage = repayPoundage;
    }

    public BigDecimal getRepayLateFee() {
        return repayLateFee;
    }

    public void setRepayLateFee(BigDecimal repayLateFee) {
        this.repayLateFee = repayLateFee;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public String getBankMobile() {
        return bankMobile;
    }

    public void setBankMobile(String bankMobile) {
        this.bankMobile = bankMobile;
    }

    public String getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(String bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getChannelRepayId() {
        return channelRepayId;
    }

    public void setChannelRepayId(String channelRepayId) {
        this.channelRepayId = channelRepayId;
    }

    public String getMainMemberId() {
        return mainMemberId;
    }

    public void setMainMemberId(String mainMemberId) {
        this.mainMemberId = mainMemberId;
    }

    public String getPayOrderNo() {
        return payOrderNo;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    public String getApplyReductNo() {
        return applyReductNo;
    }

    public void setApplyReductNo(String applyReductNo) {
        this.applyReductNo = applyReductNo;
    }

    public BigDecimal getReductAmount() {
        return reductAmount;
    }

    public void setReductAmount(BigDecimal reductAmount) {
        this.reductAmount = reductAmount;
    }

    public BigDecimal getReductPrincipal() {
        return reductPrincipal;
    }

    public void setReductPrincipal(BigDecimal reductPrincipal) {
        this.reductPrincipal = reductPrincipal;
    }

    public BigDecimal getReductInterest() {
        return reductInterest;
    }

    public void setReductInterest(BigDecimal reductInterest) {
        this.reductInterest = reductInterest;
    }

    public BigDecimal getReductOverdue() {
        return reductOverdue;
    }

    public void setReductOverdue(BigDecimal reductOverdue) {
        this.reductOverdue = reductOverdue;
    }

    public BigDecimal getReductPoundage() {
        return reductPoundage;
    }

    public void setReductPoundage(BigDecimal reductPoundage) {
        this.reductPoundage = reductPoundage;
    }
}
