package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.SmsTemplate;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.SmsService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Map;

@Component
public class NotAmountAppliedListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(NotAmountAppliedListener.class);

    @Autowired
    private WarningService warningService;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private SmsService smsService;

    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    @Autowired
    private LoanRepository loanRepository;

    public NotAmountAppliedListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.NOT_AMOUNT_APPLIED_SMS_SEND)
    public void notAmountAppliedListen(Message message, Channel channel) {
        String userRiskRecordId = new String(message.getBody(), StandardCharsets.UTF_8);
        UserRiskRecord userRiskRecord = userRiskRecordRepository.findById(userRiskRecordId).orElseThrow();
        //获取用户信息
        UserInfo user = userInfoRepository.findById(userRiskRecord.getUserId()).orElseThrow();
        //获取借款申请订单
        Order order = orderRepository.findTopByUserIdAndOrderStateOrderByCreatedTimeDesc(user.getId(), OrderState.AUDIT_PASS);
        try {
            //判断有无借款申请订单
            if (order == null || StringUtils.isBlank(order.getApplyTime().toString())) {
//                smsService.send(SmsTemplate.AMOUNT_NOT_APPLIED, Map.of(
//                    "name", user.getName(),
//                    "flowChannel", userRiskRecord.getFlowChannel().name()
//                ), user.getMobile());
                logger.info("用户{}未发起借款id为{}", user.getName(), user.getId());
            } else {
                logger.info("用户{}已发起借款id为{}", user.getName(), user.getId());
            }
        } catch (Exception e) {
            warningService.warn("风控通过后且未提交借款申请短信发送异常" + user.getId(),
                msg -> logger.error("风控通过后且未提交借款申请短信发送异常:{},", user.getId(), e));
        } finally {
            ackMsg(user.getId(), message, channel);
        }
    }
}
