package com.maguo.loan.cash.flow.entrance.common.dto.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
public class ContractReqDTO {

    /**
     * 类型
     * 1-授信阶段协议
     * 2-绑卡阶段协议
     * 3-借款阶段协议
     * 4-还款阶段协议
     */
    @NotNull(message = "协议场景不能为空")
    @Max(5)
    @Min(1)
    private Integer scene;

    /**
     * 合作机构单号
     */
    private String partnerOrderNo;

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public Integer getScene() {
        return scene;
    }

    public void setScene(Integer scene) {
        this.scene = scene;
    }
}
