package com.maguo.loan.cash.flow.enums;

/**
 * <AUTHOR>
 * @date 2024/1/19
 * 权益支付场景
 */
public enum RightsPayScene {
    /**
     * 主动支付权益
     */
    POSITIVE(false, WhetherState.N),
    /**
     * 被动支付权益
     */
    PASSIVE(true, WhetherState.Y);
    /**
     * 放款后 true
     */
    private boolean afterLoan;

    /**
     * 卡扣
     */
    private WhetherState withholdCard;

    RightsPayScene(boolean afterLoan, WhetherState withholdCard) {
        this.afterLoan = afterLoan;
        this.withholdCard = withholdCard;
    }
}
