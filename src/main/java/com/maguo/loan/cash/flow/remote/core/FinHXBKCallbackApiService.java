package com.maguo.loan.cash.flow.remote.core;

import com.jinghang.capital.api.HXBKCallbackApiService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * HXBK回调服务Feign客户端
 * 用于Flow模块调用Capital模块的HXBK回调处理
 *
 * @Author: Lior
 * @CreateTime: 2025/7/10 22:30
 */
@FeignClient(name = "capital-core-service", contextId = "hxbkCallback", path = "/hxbk/callback")
public interface FinHXBKCallbackApiService extends HXBKCallbackApiService {
}
