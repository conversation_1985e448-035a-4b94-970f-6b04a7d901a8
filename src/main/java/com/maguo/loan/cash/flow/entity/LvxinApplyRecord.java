package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "lvxin_apply_record")
public class LvxinApplyRecord extends BaseEntity {

    @Override
    protected String prefix() {
        return "LAR";
    }

    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 手机号
     */
    private String mobile;

    /**
     * 身份证正面照
     */
    private String idPositive;

    /**
     * 身份证反面照
     */
    private String idNegative;

    /**
     * 活体照片
     */
    private String livePhoto;

    /**
     * 姓名（OCR）
     */
    private String name;

    /**
     * 身份证号（OCR）
     */
    private String idCardNo;

    //OCR供应商名称
    private String facialSupplier;
    /**
     * 身份证有效（OCR）
     */
    private String idStartTime;
    private String idEndTime;
    private String idAddress;
    private String idSex;
    private String idEthnic;
    private String idIssueOrg;
    private String faceScore;
    private String education;
    private String job;
    private String workUnitName;
    private String workUnitAddress;
    private String workUnitProvinceCode;
    private String workUnitCityCode;
    private String workUnitAreaCode;
    private String monthlyIncome;
    private String email;
    private String loanPurpose;
    private String latitude;
    private String longitude;
    private String relations;
    private String deviceInfo;
    //婚姻关系
    private String marriage;

    //婚姻关系
    private String industry;

    // 居住地信息
    private String livingAddress;
    private String livingProvinceCode;
    private String livingCityCode;
    private String livingAreaCode;
    private String livingProvince;
    private String livingCity;
    private String livingArea;

    public String getWorkUnitProvinceCode() {
        return workUnitProvinceCode;
    }

    public void setWorkUnitProvinceCode(String workUnitProvinceCode) {
        this.workUnitProvinceCode = workUnitProvinceCode;
    }

    public String getWorkUnitCityCode() {
        return workUnitCityCode;
    }

    public void setWorkUnitCityCode(String workUnitCityCode) {
        this.workUnitCityCode = workUnitCityCode;
    }

    public String getWorkUnitAreaCode() {
        return workUnitAreaCode;
    }

    public void setWorkUnitAreaCode(String workUnitAreaCode) {
        this.workUnitAreaCode = workUnitAreaCode;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getIdPositive() {
        return idPositive;
    }

    public void setIdPositive(String idPositive) {
        this.idPositive = idPositive;
    }

    public String getIdNegative() {
        return idNegative;
    }

    public void setIdNegative(String idNegative) {
        this.idNegative = idNegative;
    }

    public String getLivePhoto() {
        return livePhoto;
    }

    public void setLivePhoto(String livePhoto) {
        this.livePhoto = livePhoto;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getIdStartTime() {
        return idStartTime;
    }

    public void setIdStartTime(String idStartTime) {
        this.idStartTime = idStartTime;
    }

    public String getIdEndTime() {
        return idEndTime;
    }

    public void setIdEndTime(String idEndTime) {
        this.idEndTime = idEndTime;
    }

    public String getIdAddress() {
        return idAddress;
    }

    public void setIdAddress(String idAddress) {
        this.idAddress = idAddress;
    }

    public String getIdSex() {
        return idSex;
    }

    public void setIdSex(String idSex) {
        this.idSex = idSex;
    }

    public String getIdEthnic() {
        return idEthnic;
    }

    public void setIdEthnic(String idEthnic) {
        this.idEthnic = idEthnic;
    }

    public String getIdIssueOrg() {
        return idIssueOrg;
    }

    public void setIdIssueOrg(String idIssueOrg) {
        this.idIssueOrg = idIssueOrg;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getWorkUnitName() {
        return workUnitName;
    }

    public void setWorkUnitName(String workUnitName) {
        this.workUnitName = workUnitName;
    }

    public String getWorkUnitAddress() {
        return workUnitAddress;
    }

    public void setWorkUnitAddress(String workUnitAddress) {
        this.workUnitAddress = workUnitAddress;
    }

    public String getMonthlyIncome() {
        return monthlyIncome;
    }

    public void setMonthlyIncome(String monthlyIncome) {
        this.monthlyIncome = monthlyIncome;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getRelations() {
        return relations;
    }

    public void setRelations(String relations) {
        this.relations = relations;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(String loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public String getFaceScore() {
        return faceScore;
    }

    public void setFaceScore(String faceScore) {
        this.faceScore = faceScore;
    }

    public String getFacialSupplier() {
        return facialSupplier;
    }

    public void setFacialSupplier(String facialSupplier) {
        this.facialSupplier = facialSupplier;
    }

    public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getLivingAddress() {
        return livingAddress;
    }

    public void setLivingAddress(String livingAddress) {
        this.livingAddress = livingAddress;
    }

    public String getLivingProvinceCode() {
        return livingProvinceCode;
    }

    public void setLivingProvinceCode(String livingProvinceCode) {
        this.livingProvinceCode = livingProvinceCode;
    }

    public String getLivingCityCode() {
        return livingCityCode;
    }

    public void setLivingCityCode(String livingCityCode) {
        this.livingCityCode = livingCityCode;
    }

    public String getLivingAreaCode() {
        return livingAreaCode;
    }

    public void setLivingAreaCode(String livingAreaCode) {
        this.livingAreaCode = livingAreaCode;
    }

    public String getLivingProvince() {
        return livingProvince;
    }

    public void setLivingProvince(String livingProvince) {
        this.livingProvince = livingProvince;
    }

    public String getLivingCity() {
        return livingCity;
    }

    public void setLivingCity(String livingCity) {
        this.livingCity = livingCity;
    }

    public String getLivingArea() {
        return livingArea;
    }

    public void setLivingArea(String livingArea) {
        this.livingArea = livingArea;
    }
}
