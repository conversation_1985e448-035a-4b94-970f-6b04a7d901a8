package com.maguo.loan.cash.flow.entrance.cybk.filter.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Base64 {
    private static Logger log = LoggerFactory.getLogger(Base64.class);

    public Base64() {
    }

    public static String encodeBase64(byte[] byteArray) {
        String base64String = null;

        try {
            base64String = org.apache.commons.codec.binary.Base64.encodeBase64String(byteArray);
            return base64String;
        } catch (Exception var3) {
            log.error("base64编码失败", var3);
            throw new RuntimeException(var3);
        }
    }

    public static byte[] decodeBase64(String base64String) {
        byte[] byteArray = null;

        try {
            byteArray = org.apache.commons.codec.binary.Base64.decodeBase64(base64String);
            return byteArray;
        } catch (Exception var3) {
            log.error("base64解码失败", var3);
            throw new RuntimeException(var3);
        }
    }
}
