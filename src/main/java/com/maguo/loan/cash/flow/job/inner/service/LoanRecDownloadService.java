package com.maguo.loan.cash.flow.job.inner.service;


import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entity.InnerLoanReconciliation;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.ReconciliationFile;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.ReccState;
import com.maguo.loan.cash.flow.repository.InnerLoanReconciliationRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Service
public class LoanRecDownloadService extends AbstractRecDownloadService {
    private static final Logger logger = LoggerFactory.getLogger(LoanRecDownloadService.class);

    @Autowired
    private InnerLoanReconciliationRepository loanInnerRecRepository;

    @Autowired
    private LoanRepository loanRepository;

    private static final int LOAN_NO_IDX = 0;

    private static final int LOAN_RECORD_ID_IDX = 1;
    private static final int LOAN_DATE_IDX = 2;
    private static final int AMOUNT_IDX = 3;
    private static final int PERIODS_IDX = 4;


    @Override
    public boolean reccDetail(ReconciliationFile recFile) {

        AtomicBoolean recState = new AtomicBoolean(true);
        AtomicInteger fileLines = new AtomicInteger();

        try (InputStream inputStream = getFileService().getOssFile(recFile.getOssBucket(), recFile.getOssPath());
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            reader.lines().filter(StringUtils::isNotBlank)
                .forEach(line -> {
                    fileLines.getAndIncrement();
                    try {
                        String[] lineArr = line.split(SEPARATOR);
                        InnerLoanReconciliation loanInnerRec = new InnerLoanReconciliation();
                        loanInnerRec.setRecFileId(recFile.getId());
                        loanInnerRec.setLoanNo(lineArr[LOAN_NO_IDX]);
                        Loan loan = loanRepository.findByLoanNo(loanInnerRec.getLoanNo()).orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));
                        loanInnerRec.setLoanId(loan.getId());
                        loanInnerRec.setLoanDate(LocalDate.parse(lineArr[LOAN_DATE_IDX], DateTimeFormatter.BASIC_ISO_DATE));
                        loanInnerRec.setAmount(new BigDecimal(lineArr[AMOUNT_IDX]));
                        loanInnerRec.setPeriods(Integer.valueOf(lineArr[PERIODS_IDX]));

                        // 对账规则
                        loanInnerRec.setRecState(ReccState.S);
                        if (loan.getAmount().compareTo(loanInnerRec.getAmount()) != 0
                            || !loan.getPeriods().equals(loanInnerRec.getPeriods())
                            || !loan.getLoanState().equals(ProcessState.SUCCEED)) {
                            loanInnerRec.setRecState(ReccState.F);
                            loanInnerRec.setRemark("金额|期数|状态不对应");
                            recState.set(false);
                        }
                        loanInnerRecRepository.save(loanInnerRec);

                    } catch (Exception e) {
                        recState.set(false);
                        getWarningService().warn("解析内部放款对账文件失败:" + line, msg -> logger.error(msg, e));
                    }
                });
        } catch (IOException e) {
            getWarningService().warn("解析内部放款对账文件RecFile异常:" + recFile.getId(), msg -> logger.error(msg, e));
            return false;
        }

        //成功记录条数
        int localLines = loanRepository.countByLoanStateAndLoanTimeBetween(ProcessState.SUCCEED,
            LocalDateTime.of(recFile.getFileDate(), LocalTime.MIN), LocalDateTime.of(recFile.getFileDate(), LocalTime.MAX));
        if (localLines != fileLines.get()) {
            getWarningService().warn("放款对账:成功条数不一致, =" + localLines + " , 资金=" + fileLines.get(), logger::error);
            recState.set(false);
            recFile.setRemark("本地放款成功条数不等于文件行数,=" + localLines + " , 资金=" + fileLines.get());
        }

        return recState.get();
    }

    @Override
    public FileType reccType() {
        return FileType.LOAN_FILE;
    }

}
