package com.maguo.loan.cash.flow.controller;


import com.jinghang.ppd.api.RepayApi;
import com.jinghang.ppd.api.dto.RepayTrailDto;
import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.TrailResultDto;
import com.jinghang.ppd.api.dto.repay.ReduceApplyDto;
import com.jinghang.ppd.api.dto.repay.RepayApplyDto;
import com.maguo.loan.cash.flow.convert.ManageConvert;
import com.maguo.loan.cash.flow.dto.OfflineRepayApplyRequest;
import com.maguo.loan.cash.flow.enums.WriteOffTypeEnum;
import com.maguo.loan.cash.flow.service.RepayService;
import com.maguo.loan.cash.flow.vo.RepayTrialRequest;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 还款
 */
@RestController
@RequestMapping("manage/repay")
public class ManageRepayController implements RepayApi {
    private static final Logger logger = LoggerFactory.getLogger(ManageRepayController.class);


    @Autowired
    private RepayService repayService;

    /**
     * 还款试算
     * @param trailDto
     * @return
     */
    @Override
    public RestResult<TrailResultDto> trial(RepayTrailDto trailDto) {
        RepayTrialRequest trialRequest = ManageConvert.INSTANCE.toRepayTrialRequest(trailDto);
        TrialResultVo response = repayService.trial(trialRequest.getLoanId(), trialRequest.getRepayPurpose(), trialRequest.getPeriods(),null);
        TrailResultDto dto = ManageConvert.INSTANCE.toTrailResultDto(response);
        return RestResult.success(dto);
    }

    @Override
    public RestResult<Void> reduceApply(ReduceApplyDto reduceApplyDto) {
       // offlineRepayReduceService.reduceApply(reduceApplyDto);
        return RestResult.success(null);
    }

    @Override
    public RestResult<Void> repayApply(RepayApplyDto repayApplyReq) {
        OfflineRepayApplyRequest request = ManageConvert.INSTANCE.toRepayApplyRequest(repayApplyReq);
        request.setWriteOffType(WriteOffTypeEnum.DIRECT);
        repayService.offline(request);
        return RestResult.success(null);
    }






}
