package com.maguo.loan.cash.flow.entrance.lvxin.dto.user;

/**
 * @ClassName UserCheckResponse
 * <AUTHOR>
 * @Description 用户准入 响应
 * @Date 2024/3/21 15:36
 * @Version v1.0
 **/
public class UserCheckResponse {

    /**
     * 1 - 准入通过；2 - 准入拒绝
     */
    private Integer result;

    /**
     * 拒绝原因，result=2时必传
     */
    private String reason;
    /**
     * 新老客：0-新客，1-老客（非必填）
     */
    private String apiUserType;

    public String getApiUserType() {
        return apiUserType;
    }

    public void setApiUserType(String apiUserType) {
        this.apiUserType = apiUserType;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
