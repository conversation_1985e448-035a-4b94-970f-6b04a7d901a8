package com.maguo.loan.cash.flow.entrance.lvxin.enums;

/**
 * <AUTHOR>
 * @Description 绿信放款状态
 * @Date 2024/5/22 14:47
 * @Version v1.0
 **/
public enum LvxinLoanStatus {
    INIT(3, "待放款"),
    LOANING(4, "放款中"),
    LOAN_FAIL(5, "放款失败"),
    LOAN_PASS(6, "放款成功"),
    NEED_BIND(21, "绑卡增验"),
    NEED_VERIFY(22, "借款增验");
    private int code;
    private String desc;

    LvxinLoanStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
