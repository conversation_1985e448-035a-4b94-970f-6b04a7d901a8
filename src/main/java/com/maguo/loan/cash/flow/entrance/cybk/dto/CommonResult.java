package com.maguo.loan.cash.flow.entrance.cybk.dto;

import com.alibaba.fastjson.JSONObject;
import com.jinghang.common.util.IdGen;
import com.maguo.loan.cash.flow.entrance.cybk.enums.CYBKResultCode;
import com.maguo.loan.cash.flow.entrance.ppd.enums.PpdOrderStatus;
import com.maguo.loan.cash.flow.entrance.ppd.exception.PpdResultCode;

public class CommonResult {

    private static final int MAX_ID_LENGTH = 32;

    public static CYBKCommonResponse assembleResponse(CYBKCommonRequest cybkCommonRequest, String resJson) {
        CYBKResponseHeader responseHeader = new CYBKResponseHeader();
        responseHeader.setReqNo(cybkCommonRequest.getHead().getReqNo());
        responseHeader.setRespNo(IdGen.genId("CY", MAX_ID_LENGTH - 2));
        responseHeader.setRespCode(CYBKResultCode.SUCCESS.getCode());
        responseHeader.setRespMsg(CYBKResultCode.SUCCESS.getMsg());

        CYBKCommonResponse cybkCommonResponse = new CYBKCommonResponse();
        cybkCommonResponse.setHead(responseHeader);
        cybkCommonResponse.setBody(JSONObject.parseObject(resJson));

        return cybkCommonResponse;
    }
}
