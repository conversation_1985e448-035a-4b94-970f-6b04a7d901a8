package com.maguo.loan.cash.flow.job.inner.service;


import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entity.BankRepayRecord;
import com.maguo.loan.cash.flow.entity.InnerRepaymentReconciliation;
import com.maguo.loan.cash.flow.entity.ReconciliationFile;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.ReccState;
import com.maguo.loan.cash.flow.repository.BankRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.InnerRepaymentReconciliationRepository;
import com.maguo.loan.cash.flow.repository.WithholdFlowRepository;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Service
public class RepaymentRecDownloadService extends AbstractRecDownloadService {
    private static final Logger logger = LoggerFactory.getLogger(RepaymentRecDownloadService.class);

    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;

    @Autowired
    private InnerRepaymentReconciliationRepository repaymentInnerRecRepository;

    @Autowired
    private WithholdFlowRepository withholdFlowRepository;


    private static final int REPAY_NO_IDX = 0;
    private static final int REPAY_ID_IDX = 1;
    private static final int REPAY_DATE_IDX = 2;
    private static final int REPAY_AMOUNT_IDX = 3;
    private static final int PERIOD_IDX = 4;
    private static final int PRINCIPAL_IDX = 5;
    private static final int INTEREST_IDX = 6;
    private static final int PENALTY_IDX = 7;
    private static final int GUARANTEE_IDX = 8;
    private static final int BREACH_IDX = 9;


    @Override
    public boolean reccDetail(ReconciliationFile recFile) {

        AtomicBoolean recState = new AtomicBoolean(true);
        AtomicInteger fileLines = new AtomicInteger();

        try (InputStream inputStream = getFileService().getOssFile(recFile.getOssBucket(), recFile.getOssPath());
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            reader.lines().filter(StringUtils::isNotBlank)
                .forEach(line -> {
                    fileLines.getAndIncrement();
                    try {
                        String[] lineArr = line.split(SEPARATOR);
                        InnerRepaymentReconciliation entity = new InnerRepaymentReconciliation();
                        entity.setRecFileId(recFile.getId());
                        entity.setRepayNo(lineArr[REPAY_NO_IDX]);
                        entity.setRepayId(lineArr[REPAY_ID_IDX]);
                        // 对资还款记录
                        BankRepayRecord bankRepayRecord =
                            bankRepayRecordRepository.findById(entity.getRepayId()).orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
                        entity.setLoanId(bankRepayRecord.getLoanId());
                        entity.setRepayDate(LocalDate.parse(lineArr[REPAY_DATE_IDX], DateTimeFormatter.BASIC_ISO_DATE));
                        entity.setAmount(new BigDecimal(lineArr[REPAY_AMOUNT_IDX]));
                        entity.setPeriod(Integer.valueOf(lineArr[PERIOD_IDX]));
                        entity.setPrincipalAmount(new BigDecimal(lineArr[PRINCIPAL_IDX]));
                        entity.setInterestAmount(new BigDecimal(lineArr[INTEREST_IDX]));
                        entity.setPenaltyAmount(new BigDecimal(lineArr[PENALTY_IDX]));
                        entity.setGuaranteeAmount(new BigDecimal(lineArr[GUARANTEE_IDX]));
                        entity.setBreachAmount(new BigDecimal(lineArr[BREACH_IDX]));

                        if (bankRepayRecord.getPrincipal().compareTo(entity.getPrincipalAmount()) != 0
                            || bankRepayRecord.getInterest().compareTo(entity.getInterestAmount()) != 0
                            || bankRepayRecord.getGuarantee().compareTo(entity.getGuaranteeAmount()) != 0
                            || !bankRepayRecord.getState().equals(ProcessState.SUCCEED)) {
                            entity.setRecState(ReccState.F);
                            entity.setRemark("金额|还款状态,不匹配");
                            recState.set(false);
                        }
                        repaymentInnerRecRepository.save(entity);

                    } catch (Exception e) {
                        recState.set(false);
                        getWarningService().warn("解析内部还款对账文件失败:" + line, msg -> logger.error(msg, e));
                    }
                });
        } catch (IOException e) {
            getWarningService().warn("解析内部还款对账文件RecFile异常:" + recFile.getId(), msg -> logger.error(msg, e));
            return false;
        }

        //对账日期
        LocalDate fileDate = recFile.getFileDate();

        //成功记录条数
        int localLines = bankRepayRecordRepository.countByStateAndRepayTimeBetween(ProcessState.SUCCEED,
            LocalDateTime.of(fileDate, LocalTime.MIN), LocalDateTime.of(fileDate, LocalTime.MAX));
        if (localLines != fileLines.get()) {
            getWarningService().warn("还款对账:成功条数不一致, =" + localLines + " , 资金=" + fileLines.get(), logger::error);
            recState.set(false);
            recFile.setRemark("本地还款成功条数不等于文件行数,=" + localLines + " , 资金=" + fileLines.get());
        }

        //代偿后还款/代偿前平台扣款，未通知fin-core或通知后未成功的扣款数量
        Integer unNotifyCore = withholdFlowRepository.countByDeductNotifyCoreFail(LocalDateTime.of(fileDate, LocalTime.MIN), LocalDateTime.of(fileDate,
            LocalTime.MAX));
        if (unNotifyCore > 0) {
            getWarningService().warn("还款对账:存在" + unNotifyCore + "条扣款成功后未通知到资金", logger::error);
            recState.set(false);
            recFile.setRemark("存在" + unNotifyCore + "条扣款成功后未通知到资金");
        }

        return recState.get();
    }

    @Override
    public FileType reccType() {
        return FileType.REPAYMENT_FILE;
    }

}
