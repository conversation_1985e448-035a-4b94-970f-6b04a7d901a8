package com.maguo.loan.cash.flow.repository;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import feign.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface CreditRepository extends JpaRepository<Credit, String> {

    //Credit findByUserId(String userId);

    Optional<Credit> findByUserIdAndOrderIdAndState(String userId, String orderId, ProcessState state);

    Optional<Credit> findByOrderIdAndState(String orderId, ProcessState state);

    Boolean existsByOrderIdAndStateNotIn(String orderId, ProcessState... state);

    Boolean existsByOrderIdAndStateIn(String orderId, ProcessState... state);

    /**
     * 该方法弃用，使用findByOrderIdAndState
     */
    @Deprecated(forRemoval = true)
    Optional<Credit> findByOrderId(String orderId);

    Optional<Credit> findTopByOrderIdOrderByCreatedTimeDesc(String orderId);

    @Deprecated
    Optional<Credit> findByOuterCreditIdAndState(String outerCreditId, ProcessState state);

    @Query(value = "SELECT * FROM credit c where c.bank_channel = 'QJ_LSB' and c.state = 'SUCCEED' "
        + "and c.pass_time >= ?1 and c.pass_time <= ?2 "
        + "and (not exists (select 1 FROM loan where credit_id = c.id) or exists (select 1 FROM loan where credit_id = c.id and loan_state = 'SUSPEND')) "
        + "and not exists(select 1 from `order` where id = c.order_id and order_state = 'LOAN_CANCEL')",
        nativeQuery = true)
    List<Credit> findLsbNeedRecredit(LocalDateTime beginPassTime, LocalDateTime endPassTime);

    List<Credit>  findByApplyTimeBetweenAndStateIn(LocalDateTime applyTimeStart, LocalDateTime applyTimeEnd, Collection<ProcessState> states);

    @Query("select COALESCE(sum(c.creditAmt), 0) from Credit c where c.bankChannel = :bankChannel  and c.state = :state "
        + " and c.passTime >= :beginTime and c.passTime < :endTime")
    BigDecimal sumBankCreditUsedSucceed(BankChannel bankChannel, ProcessState state, LocalDateTime beginTime, LocalDateTime endTime);

    @Query("select COALESCE(sum(c.creditAmt), 0) from Credit c where c.bankChannel = :bankChannel  and c.state in (:states) "
        + " and c.createdTime >= :beginTime and c.createdTime < :endTime")
    BigDecimal sumBankCreditUsedProcessing(BankChannel bankChannel, List<ProcessState> states, LocalDateTime beginTime, LocalDateTime endTime);

    @Query("select COALESCE(sum(c.creditAmt), 0) from Credit c where c.flowChannel = :flowChannel  and c.state = :state "
        + " and c.passTime >= :beginTime and c.passTime < :endTime")
    BigDecimal sumFlowCreditUsed(FlowChannel flowChannel, ProcessState state, LocalDateTime beginTime, LocalDateTime endTime);


    Optional<Credit> findTopByUserIdOrderByCreatedTimeDesc(String id);

    @Query("select count(*) from Credit  c "
        + "where c.state ='PROCESSING'"
        + "and c.applyTime > curdate()"
        + "and timestampdiff(minute ,c.applyTime, now())>15")
    Integer countCreditTimeout();

    @Query("select count(*) from Order o left join Credit c on o.id = c.orderId"
        + " where c.id is null and o.orderState ='CREDITING' "
        + "and timestampdiff(minute,o.applyTime,now())>5 "
        + "and o.applyTime> curdate() ")
    Integer countCreditPushTimeout();


    List<Credit> findByStateAndPassTimeBetween(ProcessState processState, LocalDateTime of, LocalDateTime of1);

    List<Credit> findAllByUserId(String userId);

    @Query("SELECT SUM(c.creditAmt) FROM Credit c WHERE c.flowChannel = :flowChannel AND c.guaranteeCompany = :guaranteeComp AND c.bankChannel = :bankChannel  AND c.state IN :state AND c.createdTime BETWEEN :startTime AND :endTime")
    Optional<BigDecimal> sumAmountByDimensionsAndStates(
        @Param("flowChannel") FlowChannel flowChannel,
        @Param("guaranteeComp") GuaranteeCompany guaranteeComp,
        @Param("bankChannel") BankChannel bankChannel,
        @Param("state") ProcessState state,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );
}
