package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.LvxinRepayApplyRecord;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR> gale
 * @Description
 * @Date 2024/2/19 16:26
 */
public interface LvxinRepayApplyRecordRepository extends JpaRepository<LvxinRepayApplyRecord, String> {

    Optional<LvxinRepayApplyRecord> findByOutRepayId(String outRepayId);
    boolean existsByOutRepayId(String outRepayId);

    LvxinRepayApplyRecord findByOutLoanIdAndLoanIdAndOutRepayId(String loanGid, String partnerOrderNo, String repaymentGid);
}
