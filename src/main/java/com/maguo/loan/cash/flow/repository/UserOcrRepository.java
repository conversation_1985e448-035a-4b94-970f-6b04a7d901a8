package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.UserOcr;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface UserOcrRepository extends JpaRepository<UserOcr, String> {

    @Query("select o from UserOcr o where o.userId = ?1 order by o.updatedTime desc limit 1")
    UserOcr findByUserId(String userId);

    List<UserOcr> findAllByUserIdIn(List<String> userIds);
}
