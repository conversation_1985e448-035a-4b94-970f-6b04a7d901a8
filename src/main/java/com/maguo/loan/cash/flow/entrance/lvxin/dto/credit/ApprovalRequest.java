package com.maguo.loan.cash.flow.entrance.lvxin.dto.credit;

import groovy.lang.GString;
import jakarta.validation.constraints.NotBlank;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName ApprovalRequest
 * <AUTHOR>
 * @Description 授信申请
 * @Date 2024/3/25 16:46
 * @Version v1.0
 **/
public class ApprovalRequest {
    /**
     * 绿信用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    /**
     * 绿信授信流水号
     */
    @NotBlank(message = "授信流水号不能为空")
    private String partnerUserId;
    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phone;
    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String name;
    /**
     * 身份证号
     */
    @NotBlank(message = "身份证不能为空")
    private String idCard;
    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 申请金额
     */
    @NotBlank(message = "申请金额不能为空")
    private BigDecimal creditAmount;

    /**
     * 申请期限
     */
    @NotBlank(message = "申请期限不能为空")
    private String applyPeriod;
    /**
     * 是否贷中用户 0 非贷中，1贷中，默认0
     */
    private Integer centerLoanUser;
    /**
     * 产品代码 01 绿信-长银,02 绿信-长银-权益类,03 绿信-湖消
     */
    private String productType;
    /**
     * 用户认证照片信息
     */
    private AuthInfo authInfo;
    /**
     * 用户基本信息
     */
    private BaseInfo baseInfo;
    /**
     * 设备信息
     */
    private DeviceInfo deviceInfo;
    /**
     * 联系人信息
     */
    private List<ContactInfo> contactInfos;

    public @NotBlank(message = "用户ID不能为空") String getUserId() {
        return userId;
    }

    public void setUserId(@NotBlank(message = "用户ID不能为空") String userId) {
        this.userId = userId;
    }

    public @NotBlank(message = "授信流水号不能为空") String getPartnerUserId() {
        return partnerUserId;
    }

    public void setPartnerUserId(@NotBlank(message = "授信流水号不能为空") String partnerUserId) {
        this.partnerUserId = partnerUserId;
    }

    public @NotBlank(message = "手机号不能为空") String getPhone() {
        return phone;
    }

    public void setPhone(@NotBlank(message = "手机号不能为空") String phone) {
        this.phone = phone;
    }

    public @NotBlank(message = "姓名不能为空") String getName() {
        return name;
    }

    public void setName(@NotBlank(message = "姓名不能为空") String name) {
        this.name = name;
    }

    public @NotBlank(message = "身份证不能为空") String getIdCard() {
        return idCard;
    }

    public void setIdCard(@NotBlank(message = "身份证不能为空") String idCard) {
        this.idCard = idCard;
    }

    public @NotBlank(message = "申请金额不能为空") BigDecimal getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(@NotBlank(message = "申请金额不能为空") BigDecimal creditAmount) {
        this.creditAmount = creditAmount;
    }

    public @NotBlank(message = "申请期限不能为空") String getApplyPeriod() {
        return applyPeriod;
    }

    public void setApplyPeriod(@NotBlank(message = "申请期限不能为空") String applyPeriod) {
        this.applyPeriod = applyPeriod;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public Integer getCenterLoanUser() {
        return centerLoanUser;
    }

    public void setCenterLoanUser(Integer centerLoanUser) {
        this.centerLoanUser = centerLoanUser;
    }

    public AuthInfo getAuthInfo() {
        return authInfo;
    }

    public void setAuthInfo(AuthInfo authInfo) {
        this.authInfo = authInfo;
    }

    public BaseInfo getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(BaseInfo baseInfo) {
        this.baseInfo = baseInfo;
    }

    public DeviceInfo getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(DeviceInfo deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public List<ContactInfo> getContactInfos() {
        return contactInfos;
    }

    public void setContactInfos(List<ContactInfo> contactInfos) {
        this.contactInfos = contactInfos;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public class AuthInfo {
        /**
         * 身份证正面
         */
        private String frontUrl;
        /**
         * 身份证反面
         */
        private String backUrl;
        /**
         * 人脸照片
         */
        private String borrower;
        /**
         * 身份证户籍地址
         */
        private String address;
        /**
         * 身份证有效期开始时间,20210320
         */
        private String startDueTimeOcr;
        /**
         * 身份证有效期结束时间,20310320、长期类型传“长期”
         */
        private String endDueTimeOcr;
        /**
         * 性别 男: 1; 女: 0;
         */
        private int sex;
        /**
         * 出生日期,20210320
         */
        private String birthday;
        /**
         * 民族
         */
        private String nation;
        /**
         * 签发机关
         */
        private String authority;
        /**
         * 人脸分
         */
        private String liveRate;

        /**
         * 人脸供应商
         */
        private String facialSupplier;

        public String getFrontUrl() {
            return frontUrl;
        }

        public void setFrontUrl(String frontUrl) {
            this.frontUrl = frontUrl;
        }

        public String getBackUrl() {
            return backUrl;
        }

        public void setBackUrl(String backUrl) {
            this.backUrl = backUrl;
        }

        public String getBorrower() {
            return borrower;
        }

        public void setBorrower(String borrower) {
            this.borrower = borrower;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getStartDueTimeOcr() {
            return startDueTimeOcr;
        }

        public void setStartDueTimeOcr(String startDueTimeOcr) {
            this.startDueTimeOcr = startDueTimeOcr;
        }

        public String getEndDueTimeOcr() {
            return endDueTimeOcr;
        }

        public void setEndDueTimeOcr(String endDueTimeOcr) {
            this.endDueTimeOcr = endDueTimeOcr;
        }

        public int getSex() {
            return sex;
        }

        public void setSex(int sex) {
            this.sex = sex;
        }

        public String getBirthday() {
            return birthday;
        }

        public void setBirthday(String birthday) {
            this.birthday = birthday;
        }

        public String getNation() {
            return nation;
        }

        public void setNation(String nation) {
            this.nation = nation;
        }

        public String getAuthority() {
            return authority;
        }

        public void setAuthority(String authority) {
            this.authority = authority;
        }

        public String getLiveRate() {
            return liveRate;
        }

        public void setLiveRate(String liveRate) {
            this.liveRate = liveRate;
        }

        public String getFacialSupplier() {
            return facialSupplier;
        }

        public void setFacialSupplier(String facialSupplier) {
            this.facialSupplier = facialSupplier;
        }
    }

    public class BaseInfo {
        private String maritalStatus;
        private String educational;
        private String companyAddress;
        private String companyName;
        private String companyPhone;
        private String companyProvince;
        private String companyCity;
        private String companyArea;
        private String companyProvinceCode;
        private String companyCityCode;
        private String companyAreaCode;
        private String inCome;
        private String liveAddress;
        private String liveProvince;
        private String liveCity;
        private String liveArea;
        private String provinceCode;
        private String cityCode;
        private String areaCode;
        private String workType;
        private String industry;
        private String workingYears;
        private String department;
        private String companyType;
        private String position;
        private String loanPurpose;
        /**
         * 住房类型：101-租房，102-自建房，103-商品房无贷款，104商品房有贷款，105-宿舍，106-父母同住，100-其他
         */
        private String houseType;
        private String email;
        /**
         * 流量标签：1-优质渠道，0-普通渠道
         */
        private Integer channelLabel;

        public String getMaritalStatus() {
            return maritalStatus;
        }

        public void setMaritalStatus(String maritalStatus) {
            this.maritalStatus = maritalStatus;
        }

        public String getEducational() {
            return educational;
        }

        public void setEducational(String educational) {
            this.educational = educational;
        }

        public String getCompanyAddress() {
            return companyAddress;
        }

        public void setCompanyAddress(String companyAddress) {
            this.companyAddress = companyAddress;
        }

        public String getCompanyName() {
            return companyName;
        }

        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }

        public String getCompanyPhone() {
            return companyPhone;
        }

        public void setCompanyPhone(String companyPhone) {
            this.companyPhone = companyPhone;
        }

        public String getCompanyProvince() {
            return companyProvince;
        }

        public void setCompanyProvince(String companyProvince) {
            this.companyProvince = companyProvince;
        }

        public String getCompanyCity() {
            return companyCity;
        }

        public void setCompanyCity(String companyCity) {
            this.companyCity = companyCity;
        }

        public String getCompanyArea() {
            return companyArea;
        }

        public void setCompanyArea(String companyArea) {
            this.companyArea = companyArea;
        }

        public String getCompanyProvinceCode() {
            return companyProvinceCode;
        }

        public void setCompanyProvinceCode(String companyProvinceCode) {
            this.companyProvinceCode = companyProvinceCode;
        }

        public String getCompanyCityCode() {
            return companyCityCode;
        }

        public void setCompanyCityCode(String companyCityCode) {
            this.companyCityCode = companyCityCode;
        }

        public String getCompanyAreaCode() {
            return companyAreaCode;
        }

        public void setCompanyAreaCode(String companyAreaCode) {
            this.companyAreaCode = companyAreaCode;
        }

        public String getInCome() {
            return inCome;
        }

        public void setInCome(String inCome) {
            this.inCome = inCome;
        }

        public String getLiveAddress() {
            return liveAddress;
        }

        public void setLiveAddress(String liveAddress) {
            this.liveAddress = liveAddress;
        }

        public String getLiveProvince() {
            return liveProvince;
        }

        public void setLiveProvince(String liveProvince) {
            this.liveProvince = liveProvince;
        }

        public String getLiveCity() {
            return liveCity;
        }

        public void setLiveCity(String liveCity) {
            this.liveCity = liveCity;
        }

        public String getLiveArea() {
            return liveArea;
        }

        public void setLiveArea(String liveArea) {
            this.liveArea = liveArea;
        }

        public String getProvinceCode() {
            return provinceCode;
        }

        public void setProvinceCode(String provinceCode) {
            this.provinceCode = provinceCode;
        }

        public String getCityCode() {
            return cityCode;
        }

        public void setCityCode(String cityCode) {
            this.cityCode = cityCode;
        }

        public String getAreaCode() {
            return areaCode;
        }

        public void setAreaCode(String areaCode) {
            this.areaCode = areaCode;
        }

        public String getWorkType() {
            return workType;
        }

        public void setWorkType(String workType) {
            this.workType = workType;
        }

        public String getIndustry() {
            return industry;
        }

        public void setIndustry(String industry) {
            this.industry = industry;
        }

        public String getWorkingYears() {
            return workingYears;
        }

        public void setWorkingYears(String workingYears) {
            this.workingYears = workingYears;
        }

        public String getDepartment() {
            return department;
        }

        public void setDepartment(String department) {
            this.department = department;
        }

        public String getCompanyType() {
            return companyType;
        }

        public void setCompanyType(String companyType) {
            this.companyType = companyType;
        }

        public String getPosition() {
            return position;
        }

        public void setPosition(String position) {
            this.position = position;
        }

        public String getLoanPurpose() {
            return loanPurpose;
        }

        public void setLoanPurpose(String loanPurpose) {
            this.loanPurpose = loanPurpose;
        }

        public String getHouseType() {
            return houseType;
        }

        public void setHouseType(String houseType) {
            this.houseType = houseType;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public Integer getChannelLabel() {
            return channelLabel;
        }

        public void setChannelLabel(Integer channelLabel) {
            this.channelLabel = channelLabel;
        }
    }
}
