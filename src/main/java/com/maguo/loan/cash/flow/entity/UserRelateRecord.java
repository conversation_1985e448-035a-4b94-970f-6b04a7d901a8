package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * <AUTHOR>
 * @since 2025-02-24
 */
@Entity
@Table(name = "user_relate_record")
public class UserRelateRecord extends BaseEntity {

    private String userId;

    private String strategyId;

    /**
     * 策略分箱id
     */
    private String strategyRelateId;

    private String strategyRelateCode;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId;
    }

    public String getStrategyRelateId() {
        return strategyRelateId;
    }

    public void setStrategyRelateId(String strategyBinningId) {
        this.strategyRelateId = strategyBinningId;
    }

    public String getStrategyRelateCode() {
        return strategyRelateCode;
    }

    public void setStrategyRelateCode(String strategyBinningCode) {
        this.strategyRelateCode = strategyBinningCode;
    }

    @Override
    protected String prefix() {
        return "URR";
    }
}
