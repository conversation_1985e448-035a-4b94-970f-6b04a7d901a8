package com.maguo.loan.cash.flow.enums;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
public enum LoanPurpose {
    SHOPPING("购物"),
    HEALTH("医疗健康"),
    TOUR("旅游度假"),
    MARRIAGE("婚庆"),
    DECORATION("店面装修"),
    OTHER("其他"),
    EDUCATION("教育培训");

    private final String desc;

    LoanPurpose(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public static LoanPurpose LoanPurpose(String type) {
        return switch (type) {
            case "01" ->LoanPurpose.SHOPPING;
            case "02" ->LoanPurpose.HEALTH;
            case "03" -> LoanPurpose.TOUR;
            case "04" -> LoanPurpose.MARRIAGE;
            case "05" ->LoanPurpose.DECORATION;
            case "06" -> LoanPurpose.OTHER;
            case "07" ->LoanPurpose.EDUCATION;
            default -> LoanPurpose.OTHER;
        };
    }
}
