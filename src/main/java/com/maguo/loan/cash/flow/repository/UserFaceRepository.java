package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.UserFace;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface UserFaceRepository extends JpaRepository<UserFace, String> {

    @Query("select f from UserFace f where f.userId = ?1 order by f.updatedTime desc limit 1")
    UserFace findByUserId(String userId);

}
