package com.maguo.loan.cash.flow.remote.riskdata.req;

import java.util.Map;

/**
* <AUTHOR>
* @Description 内部风控系统请求
* @Date 2025-05-23
* @Version 1.0
*/
public class RiskDataRequest {

    /**
     * 决策流id CD开头+4位数字
     */
    private String callSerialNumber;
    /**
     * 接口密钥
     */
    private String systemKey;
    /**
     * 进件要素
     */
    private Map inputParamMap;

    public String getCallSerialNumber() {
        return callSerialNumber;
    }

    public void setCallSerialNumber(String callSerialNumber) {
        this.callSerialNumber = callSerialNumber;
    }

    public String getSystemKey() {
        return systemKey;
    }

    public void setSystemKey(String systemKey) {
        this.systemKey = systemKey;
    }

    public Map getInputParamMap() {
        return inputParamMap;
    }

    public void setInputParamMap(Map inputParamMap) {
        this.inputParamMap = inputParamMap;
    }
}
