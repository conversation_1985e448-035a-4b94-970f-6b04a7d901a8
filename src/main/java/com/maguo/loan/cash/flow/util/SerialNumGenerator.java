package com.maguo.loan.cash.flow.util;

import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class SerialNumGenerator {


    private static StringRedisTemplate redisTemplate;

    @Autowired
    public void setRedissonClient(StringRedisTemplate redisTemplate) {
        SerialNumGenerator.redisTemplate = redisTemplate;
    }



    private static final String REDIS_KEY = "JH:LOAN-CASH-FLOW:SERIAL";

    private static final DateTimeFormatter TIMESTAMP_FORMATTER =
        DateTimeFormatter.ofPattern("yyyyMMddHHmmss");


    private static final String DIGITS = "0123456789";

    public static  String generateSerial(){


        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMATTER);
        Long increment = redisTemplate.opsForValue().increment(REDIS_KEY, 1);

        if (increment != null && increment == 1) {
            redisTemplate.expire(REDIS_KEY, 1, TimeUnit.DAYS);
        }

        String randomPart = generateSecureRandom(12);


        // 组合
        return String.format("%s%06d%s", timestamp,
            (increment != null ? increment % 1_000_000 : 0),
            randomPart);

    }


    private static String generateSecureRandom(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = (int) (Math.random() * DIGITS.length());
            sb.append(DIGITS.charAt(index));
        }
        return sb.toString();
    }


    public static void main(String[] args) {
        String s = SerialNumGenerator.generateSerial();
        System.out.println(s);
    }




}
