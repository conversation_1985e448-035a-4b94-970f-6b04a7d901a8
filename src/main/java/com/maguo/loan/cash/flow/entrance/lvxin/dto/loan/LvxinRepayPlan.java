package com.maguo.loan.cash.flow.entrance.lvxin.dto.loan;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 还款计划
 * @Date 2024/5/21 11:09
 * @Version v1.0
 **/
public class LvxinRepayPlan {
    /**
     * 期数
     */
    private Integer period;
    /**
     * 本期应还金额
     */
    private BigDecimal repayMoney;
    /**
     * 本期应还本金
     */
    private BigDecimal principal;
    /**
     * 实还本机
     */
    private BigDecimal principalRepaid;
    /**
     * 应还利息
     */
    private BigDecimal interest;
    /**
     * 实还利息
     */
    private BigDecimal interestRepaid;
    /**
     * 应还罚息
     */
    private BigDecimal overdueFee;
    /**
     * 实还罚息
     */
    private BigDecimal overdueFeeRepaid;
    /**
     * 应还咨询费
     */
    private BigDecimal consultFee;
    /**
     * 实还咨询费
     */
    private BigDecimal actConsultFee;
    /**
     * 应还咨询费
     */
    private BigDecimal fee;
    /**
     * 实还咨询费
     */
    private BigDecimal feeRepaid;

    /**
     * 提前还款违约金
     */
    private BigDecimal rightFee;
    /**
     * 实还提前还款违约金
     */
    private BigDecimal rightFeeRepaid;
    /**
     * 到期时间 yyyy-MM-dd
     */
    private String dueTime;
    /**
     * 是否逾期
     */
    @JsonProperty("isOverdue")
    private Boolean isOverdue;
    /**
     * 还款状态 0未还款，1部分还款，2已还本金，3已结清
     */
    private Integer repaymentStatus;
    /**
     * 实际还款时间，已还情况有效，未还时为0.
     * 秒级时间戳
     */
    private Long lastRepaymentTime;

    public BigDecimal getConsultFee() {
        return consultFee;
    }

    public void setConsultFee(BigDecimal consultFee) {
        this.consultFee = consultFee;
    }

    public BigDecimal getActConsultFee() {
        return actConsultFee;
    }

    public void setActConsultFee(BigDecimal actConsultFee) {
        this.actConsultFee = actConsultFee;
    }

    public BigDecimal getPrincipalRepaid() {
        return principalRepaid;
    }

    public void setPrincipalRepaid(BigDecimal principalRepaid) {
        this.principalRepaid = principalRepaid;
    }

    public BigDecimal getInterestRepaid() {
        return interestRepaid;
    }

    public void setInterestRepaid(BigDecimal interestRepaid) {
        this.interestRepaid = interestRepaid;
    }

    public BigDecimal getFeeRepaid() {
        return feeRepaid;
    }

    public void setFeeRepaid(BigDecimal feeRepaid) {
        this.feeRepaid = feeRepaid;
    }

    public BigDecimal getOverdueFeeRepaid() {
        return overdueFeeRepaid;
    }

    public void setOverdueFeeRepaid(BigDecimal overdueFeeRepaid) {
        this.overdueFeeRepaid = overdueFeeRepaid;
    }

    public BigDecimal getRightFeeRepaid() {
        return rightFeeRepaid;
    }

    public void setRightFeeRepaid(BigDecimal rightFeeRepaid) {
        this.rightFeeRepaid = rightFeeRepaid;
    }

    public Boolean getIsOverdue() {
        return isOverdue;
    }

    public void setIsOverdue(Boolean overdue) {
        isOverdue = overdue;
    }

    public Integer getRepaymentStatus() {
        return repaymentStatus;
    }

    public void setRepaymentStatus(Integer repaymentStatus) {
        this.repaymentStatus = repaymentStatus;
    }

    public Long getLastRepaymentTime() {
        return lastRepaymentTime;
    }

    public void setLastRepaymentTime(Long lastRepaymentTime) {
        this.lastRepaymentTime = lastRepaymentTime;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BigDecimal getRepayMoney() {
        return repayMoney;
    }

    public void setRepayMoney(BigDecimal repayMoney) {
        this.repayMoney = repayMoney;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getOverdueFee() {
        return overdueFee;
    }

    public void setOverdueFee(BigDecimal overdueFee) {
        this.overdueFee = overdueFee;
    }

    public BigDecimal getRightFee() {
        return rightFee;
    }

    public void setRightFee(BigDecimal rightFee) {
        this.rightFee = rightFee;
    }

    public String getDueTime() {
        return dueTime;
    }

    public void setDueTime(String dueTime) {
        this.dueTime = dueTime;
    }
}
