package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 贷前审批实体类
 */
@Entity
@Table(name = "pre_loan_audit_record")
public class PreLoanAuditRecord extends BaseEntity {
    /**
     * 用户
     */
    private String userId;

    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    /**
     * 风控id
     */
    private String creditId;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 借据ID
     */
    private String loanId;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 审批结果代码 AA AD AP
     */
    private String approveResultCode;

    @Enumerated(EnumType.STRING)
    private AuditState approveResult;

    /**
     * 风控最终执行返回结果
     */
    private String riskFinalResult;


    /**
     * 通过时间
     */
    private LocalDateTime passTime;

    /**
     * 有效期
     */
    private LocalDate expireTime;

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public AuditState getApproveResult() {
        return approveResult;
    }

    public void setApproveResult(AuditState approveResult) {
        this.approveResult = approveResult;
    }

    public String getApproveResultCode() {
        return approveResultCode;
    }

    public void setApproveResultCode(String approveResultCode) {
        this.approveResultCode = approveResultCode;
    }

    public LocalDateTime getPassTime() {
        return passTime;
    }

    public void setPassTime(LocalDateTime passTime) {
        this.passTime = passTime;
    }

    public LocalDate getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDate expireTime) {
        this.expireTime = expireTime;
    }

    public String getRiskFinalResult() {
        return riskFinalResult;
    }

    public void setRiskFinalResult(String riskFinalResult) {
        this.riskFinalResult = riskFinalResult;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
