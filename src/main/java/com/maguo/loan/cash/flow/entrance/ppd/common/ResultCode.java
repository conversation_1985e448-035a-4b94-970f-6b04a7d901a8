package com.maguo.loan.cash.flow.entrance.ppd.common;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
public enum ResultCode {
    SUCCESS("000000", "success"),
    SIGN_VERIFY_ERROR("100000", "签名验证失败"),
    PARAM_ILLEGAL("4001", "参数不合法"),

    UNKNOWN_CHANNEL("4003", "未知渠道号"),

    BIZ_ERROR("100002", "业务异常"),
    METHOD_NOT_SUPPORT("100003", "不支持非POST请求"),

    RISK_RECORD_NOT_EXIST("100004", "风控记录不存在"),

    USER_NOT_EXIST("100005", "用户不存在"),

    CARD_NOT_SUPPORT("500002", "不支持的银行卡", false),

    LOAN_REPEAT_SUBMIT("600001", "放款重复提交"),
    LOAN_APPLY_NOT_EXIST("600002", "放款不存在"),
    IMAGE_NOT_EXIST("600003", "影像文件缺失"),

    REPAY_REPEAT_SUBMIT("700001", "还款重复提交"),
    REPAY_NOT_EXIST("700002", "还款流水不存在"),
    EXIST_PROCESSING_REPAY("700003", "还款处理中，请稍后再发起还款"),
    REPAY_NOT_SUPPORTED_LOAN_DATE("700004", "放款日当天不允许发起还款"),
    REPAY_CLEAR_NOT_SUPPORTED_OVERDUE("700005", "逾期不允许发起提前结清还款"),
    REPAY_PERIOD_ERROR("700006", "还款期数有误"),
    REPAY_AMOUNT_ERROR("700007", "还款金额有误"),
    REPAY_AlREADY_REPAID("700008", "该期已还"),
    REPAY_MODE_ERROR("700009", "还款模式错误"),
    REPAY_REDUCE_ERROR("700010", "减免订单不存在或已失效"),
    REPAY_CATEGORY_ERROR("700011", "还款类别错误"),

    NO_SUBMIT_REPEAT("555555", "请勿重复提交"),
    CREDIT_ERROR("777777", "授信异常"),
    LOAN_ERROR("888888", "放款异常"),
    SYS_ERROR("999999", "系统异常"),
    FILE_UPLOAD_ERROR("444444", "文件上传异常"),
    SYS_MAINTAIN("900000", "系统维护"),
    CREDIT_NOT_EXIST_SUCCEED("200002", "不存在成功的授信记录"),
    CREDIT_NOT_FOUND("200003", "未找到授信记录"),
    CREDIT_AMOUNT_ERROR("200004", "授信金额错误"),
    LOAN_NOT_EXIST("600001", "借据不存在"),
    LOAN_NOT_SUCCEED("600002", "借据不成功"),
    REPAY_TRAIL_FAIL("800001", "试算失败"),
    REPAY_QUERY_ERROR("800002", "查询还款结果异常"),
    REPPAY_PLAN_CHECK_ERROR("600001", "还款计划校验异常,请重试"),
    REPAY_NOT_SUPPORTED_CURRENT_ADVANCE("700002", "试算失败,当期提前还款不支持"),
    REPAY_APPLY_REQUEST_FAIL("700003", "还款申请失败"),
    REPAY_PLAN_NORMAL_NOT_EXIST("700008", "未查询到待还记录"),

    LIVENESS_FAILURE("100032", "获取活体检测失败"),
    LOAN_NOT_FOUND("100037", "借款未找到"),
    //FTP文件不存在
    FTP_FILE_NOT_EXIST("600006", "FTP文件不存在"),
    //AGREEMENT_NOT_EXIST
    AGREEMENT_NOT_EXIST("600007", "协议不存在"),
    USER_ACCOUNT_NOT_EXIST("100008", "用户未注册!"),


    PUSH_NEW_CASE_ERROR("100034", "推送新案失败"),

    PUSH_UPDATE_CASE_ERROR("100035", "推送更新案件失败"),
    PUSH_COMPLAINT_ERROR("100036", "推送投诉信息失败"),
    PUSH_REMARK_ERROR("100037", "推送备注信息失败"),
    PUSH_ADD_REPAY_ERROR("100038", "推送新增还款失败"),
    PUSH_PAYMENT_RECEIPT_ERROR("100039", "推送回执信息失败"),
    PUSH_REPAY_RECORD_INFO_ERROR("100040", "推送还款记录信息失败");





    private final String code;
    private final String msg;

    /**
     * 是否告警,默认告警
     * 在全局异常拦截器判断该字段为true时，则告警
     */
    private final Boolean isWarning;

    ResultCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
        this.isWarning = true;
    }

    ResultCode(String code, String msg, Boolean isWarning) {
        this.code = code;
        this.msg = msg;
        this.isWarning = isWarning;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static boolean isSuccess(String code) {
        return SUCCESS.getCode().equals(code);
    }

    public Boolean getWarning() {
        return isWarning;
    }
}
