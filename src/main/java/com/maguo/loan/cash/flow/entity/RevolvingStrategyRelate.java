package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;

/**
 * 额度策略分箱表
 */
@Entity
@Table(name = "revolving_strategy_relate")
public class RevolvingStrategyRelate extends BaseEntity {

    /**
     * 策略id
     */
    private String strategyId;

    private String relateCode;

    /**
     * 前端可用额度
     */
    private BigDecimal frontAmount;

    /**
     * 分流比例
     */
    private BigDecimal ratio;

    public String getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId;
    }

    public String getRelateCode() {
        return relateCode;
    }

    public void setRelateCode(String releateCode) {
        this.relateCode = releateCode;
    }

    public BigDecimal getFrontAmount() {
        return frontAmount;
    }

    public void setFrontAmount(BigDecimal frontAmount) {
        this.frontAmount = frontAmount;
    }

    public BigDecimal getRatio() {
        return ratio;
    }

    public void setRatio(BigDecimal ratio) {
        this.ratio = ratio;
    }

    @Override
    protected String prefix() {
        return "RSR";
    }
}
