package com.maguo.loan.cash.flow.job.susupend;


import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.service.common.loan.LoanCommonService;

import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> gale
 * @Classname Suspend
 * @Description 挂起激活任务
 * @Date 2024/1/6 14:00
 */

@Component
@JobHandler("suspendActivationJob")
public class SuspendActivationJob extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(SuspendActivationJob.class);

    @Autowired
    private LoanRepository loanRepository;

    private static final Integer PAGE_NUM = 0;
    private static final Integer PAGE_SIZE = 1000;

    @Autowired
    private LoanCommonService loanCommonService;

    public static final int LOCK_WAIT_SECOND = 3;

    public static final int LOCK_RELEASE_SECOND = 8;


    @Override
    public void doJob(JobParam jobParam) {
        logger.info("挂起激活任务开始执行,jobParam:{}", JsonUtil.toJsonString(jobParam));

        List<String> loanIds = Optional.ofNullable(jobParam).map(JobParam::getLoanIds).orElse(null);
        if (CollectionUtil.isNotEmpty(loanIds)) {
            for (String loanId : loanIds) {
                loanCommonService.suspendActive(loanId);
            }
            return;
        }

        //开始循环的id，每次循环后更新
        String id = "";

        while (true) {
            PageRequest pageRequest = PageRequest.of(PAGE_NUM, PAGE_SIZE, Sort.by(Sort.Direction.ASC, "id"));

            //查询挂起的借据
            List<Loan> loans = loanRepository.findSuspendLoan(id, pageRequest);
            if (CollectionUtils.isEmpty(loans)) {
                logger.info("挂起激活任务开始执行完成");
                break;
            }
            for (Loan loan : loans) {
                loanCommonService.suspendActive(loan.getId());
            }
            //重置最大的id
            id = Objects.requireNonNull(CollectionUtils.lastElement(loans)).getId();
        }
    }


}
