package com.maguo.loan.cash.flow.enums;

//import com.qiangyun.fin.api.dto.BankChannel;
//import com.qiangyun.fin.api.dto.GuaranteeCompany;

/**
 * 代扣商户配置
 *
 * <AUTHOR>
 */
public enum WithholdAccount {

    //private final BankChannel bankChannel;
    //
    //private final GuaranteeCompany guaranteeCompany;
    //
    //private final WithholdMode withholdMode;
    //
    //private final String mainMemberType;
    //
    //private final String mainMemberId;
    //
    //private final String principalShareMemberId;
    //
    //private final String guaranteeShareMemberId;
    //
    //private final String feeMemberId;
    //
    //private final String callFeeMemberId;

}
