package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.UserContactInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface UserContactInfoRepository extends JpaRepository<UserContactInfo, String> {
    @Transactional
    void deleteByUserId(String userId);

    List<UserContactInfo> findByUserId(String userId);

    void deleteAllByNameIn(List<String> nameList);

    void deleteByUserIdAndNameIn(String userId, List<String> nameList);

    List<UserContactInfo> queryByUserIdOrderByUpdatedTimeDesc(String userId);
}
