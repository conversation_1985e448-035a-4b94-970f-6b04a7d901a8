package com.maguo.loan.cash.flow.convert;


import com.maguo.loan.cash.flow.dto.user.IdCardInfoResponse;
import com.maguo.loan.cash.flow.dto.user.IdCardInfoSaveRequest;
import com.maguo.loan.cash.flow.dto.user.UserContactDTO;
import com.maguo.loan.cash.flow.dto.user.UserInfoDTO;
import com.maguo.loan.cash.flow.entity.UserContactInfo;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.enums.DictEnum;
import com.maguo.loan.cash.flow.enums.Education;
import com.maguo.loan.cash.flow.enums.Gender;
import com.maguo.loan.cash.flow.enums.Industry;
import com.maguo.loan.cash.flow.enums.Marriage;
import com.maguo.loan.cash.flow.enums.Position;
import com.maguo.loan.cash.flow.enums.Relation;
import com.maguo.loan.cash.flow.util.BaseConstants;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-12
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserConvert {

    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);

    int CERT_NO_CITY_LENGTH = 4;
    int CERT_NO_DISTRICT_LENGTH = 6;

    default Marriage toMarriage(String marriageStr) {
        DictEnum.MaritalStatusType marriage = DictEnum.MaritalStatusType.getByCode(marriageStr);
        if (marriage == null) {
            marriage = DictEnum.MaritalStatusType.UNMARRIED;
        }
        return switch (marriage) {
            case UNMARRIED -> Marriage.UNMARRIED;
            case MARRIED -> Marriage.MARRIED;
            case DIVORCED -> Marriage.DIVORCED;
            case WIDOWED -> Marriage.WIDOWED;
            case OTHER -> Marriage.UNKNOWN;
        };
    }

    default Education toEducation(String educationStr) {

        DictEnum.Education education = DictEnum.Education.getByCode(educationStr);
        if (education == null) {
            education = DictEnum.Education.HIGH_SCHOOL;
        }

        return switch (education) {
            case HIGH_SCHOOL -> Education.HIGH_SCHOOL;
            case COLLEGE -> Education.JUNIOR_COLLEGE;
            case UNDERGRADUATE -> Education.COLLEGE;
            case POSTGRADUATE -> Education.MASTER;
        };
    }

    default Integer toIncome(String incomeTypeStr) {
        DictEnum.MonthlyIncomeType incomeType = DictEnum.MonthlyIncomeType.getByCode(incomeTypeStr);
        if (incomeType == null) {
            incomeType = DictEnum.MonthlyIncomeType.BELOW_3000;
        }
        return incomeType.getMoney();
    }

    default Industry toIndustry(String industryStr) {
        DictEnum.IndustryType industry = DictEnum.IndustryType.getByCode(industryStr);
        if (industry == null) {
            return Industry.TWENTY;
        }
        return switch (industry) {
            case BUILDING -> Industry.ONE;
            case POWER -> Industry.TWO;
            case TRANSPORTATION -> Industry.THREE;
            case EDUCATION -> Industry.FOUR;
            case REAL_ESTATE -> Industry.FIVE;
            case ACCOMMODATION -> Industry.SIX;
            case WHOLESALE_RETAIL -> Industry.SEVEN;
            case RENTAL_BUSINESS -> Industry.EIGHT;
            case INFORMATION -> Industry.NINE;
            case FINANCE -> Industry.TEN;
            case CULTURE -> Industry.ELEVEN;
            case MANUFACTURING -> Industry.TWELVE;
            case WATER_ENVIRONMENT -> Industry.THIRTEEN;
            case MINING -> Industry.FOURTEEN;
            case AGRICULTURE -> Industry.FIFTEEN;
            case RESIDENTIAL_REPAIR -> Industry.SIXTEEN;
            case HEALTHCARE -> Industry.SEVENTEEN;
            case RESEARCH_TECHNOLOGY -> Industry.EIGHTEEN;
            case PUBLIC_MANAGEMENT -> Industry.NINETEEN;
            default -> Industry.TWENTY;
        };
    }

    default Position toPosition(String jobStr) {

        DictEnum.JobCategoryType job = DictEnum.JobCategoryType.getByCode(jobStr);
        if (job == null) {
            job = DictEnum.JobCategoryType.OTHER;
        }
        return switch (job) {
            case OTHER -> Position.ELEVEN;
            case FRONT_LINE_WORKER -> Position.THIRTEEN;
            case EDUCATOR -> Position.FOURTEEN;
            case SALES_PERSON -> Position.FIFTEEN;
            case SELF_EMPLOYED -> Position.SIXTEEN;
            case PROPERTY_STAFF -> Position.SEVENTEEN;
            case SERVICE_STAFF -> Position.EIGHTEEN;
            case GOVERNMENT_EMPLOYEE -> Position.NINETEEN;
            case MEDICAL_PROFESSIONAL -> Position.TWENTY;
            case TECHNICAL_PROFESSIONAL -> Position.TWENTY_ONE;
            case MILITARY -> Position.TWENTY_TWO;
        };

    }

    default List<UserContactInfo> toUserContactInfoList(List<UserContactDTO> contactDTOS, String userId) {
        List<UserContactInfo> list = new ArrayList<>();
        for (UserContactDTO contactInfo : contactDTOS) {
            UserContactInfo userContactInfo = new UserContactInfo();
            userContactInfo.setPhone(contactInfo.getMobile());
            userContactInfo.setName(contactInfo.getName());
            userContactInfo.setUserId(userId);
            String userRelation = contactInfo.getUserRelation();
            DictEnum.RelationshipType relationshipType = DictEnum.RelationshipType.getByCode(userRelation);
            userContactInfo.setRelation(toRelationship(relationshipType));
            list.add(userContactInfo);
        }
        return list;
    }

    default Relation toRelationship(DictEnum.RelationshipType relationShip) {
        if (relationShip == null) {
            return Relation.UNKNOWN;
        }
        return switch (relationShip) {
            case PARENTS -> Relation.PARENTS;
            case RELATIVES -> Relation.RELATIVE;
            case COLLEAGUE -> Relation.COLLEAGUE;
            case FRIEND -> Relation.FRIEND;
            case OTHER -> Relation.UNKNOWN;
        };
    }

    @Mapping(source = "income", target = "monthIncome", qualifiedByName = "toIncomeInfo")
    @Mapping(source = "education", target = "education", qualifiedByName = "toDtoEducation")
    @Mapping(source = "marriage", target = "marriage", qualifiedByName = "toDtoMarriage")
    @Mapping(source = "industry", target = "industryType", qualifiedByName = "toDtoIndustry")
    @Mapping(source = "position", target = "workType", qualifiedByName = "toDtoWorkType")
    @Mapping(source = "unit", target = "workUnit")
    @Mapping(source = "unitAddress", target = "workAddress")
    UserInfoDTO toUserInfoDTO(UserInfo userInfo);

    @Named("toDtoWorkType")
    default String toDtoWorkType(Position position) {
        if (position == null) {
            return null;
        }
        return switch (position) {
            case ELEVEN -> DictEnum.JobCategoryType.OTHER.getCode();
            case THIRTEEN -> DictEnum.JobCategoryType.FRONT_LINE_WORKER.getCode();
            case FOURTEEN -> DictEnum.JobCategoryType.EDUCATOR.getCode();
            case FIFTEEN -> DictEnum.JobCategoryType.SALES_PERSON.getCode();
            case SIXTEEN -> DictEnum.JobCategoryType.SELF_EMPLOYED.getCode();
            case SEVENTEEN -> DictEnum.JobCategoryType.PROPERTY_STAFF.getCode();
            case EIGHTEEN -> DictEnum.JobCategoryType.SERVICE_STAFF.getCode();
            case NINETEEN -> DictEnum.JobCategoryType.GOVERNMENT_EMPLOYEE.getCode();
            case TWENTY -> DictEnum.JobCategoryType.MEDICAL_PROFESSIONAL.getCode();
            case TWENTY_ONE -> DictEnum.JobCategoryType.TECHNICAL_PROFESSIONAL.getCode();
            case TWENTY_TWO -> DictEnum.JobCategoryType.MILITARY.getCode();
            default -> DictEnum.JobCategoryType.OTHER.getCode();
        };
    }

    @Named("toDtoIndustry")
    default String toDtoIndustry(Industry industry) {
        if (industry == null) {
            return null;
        }
        return switch (industry) {
            case ONE -> DictEnum.IndustryType.BUILDING.getCode();
            case TWO -> DictEnum.IndustryType.POWER.getCode();
            case THREE -> DictEnum.IndustryType.TRANSPORTATION.getCode();
            case FOUR -> DictEnum.IndustryType.EDUCATION.getCode();
            case FIVE -> DictEnum.IndustryType.REAL_ESTATE.getCode();
            case SIX -> DictEnum.IndustryType.ACCOMMODATION.getCode();
            case SEVEN -> DictEnum.IndustryType.WHOLESALE_RETAIL.getCode();
            case EIGHT -> DictEnum.IndustryType.RENTAL_BUSINESS.getCode();
            case NINE -> DictEnum.IndustryType.INFORMATION.getCode();
            case TEN -> DictEnum.IndustryType.FINANCE.getCode();
            case ELEVEN -> DictEnum.IndustryType.CULTURE.getCode();
            case TWELVE -> DictEnum.IndustryType.MANUFACTURING.getCode();
            case THIRTEEN -> DictEnum.IndustryType.WATER_ENVIRONMENT.getCode();
            case FOURTEEN -> DictEnum.IndustryType.MINING.getCode();
            case FIFTEEN -> DictEnum.IndustryType.AGRICULTURE.getCode();
            case SIXTEEN -> DictEnum.IndustryType.RESIDENTIAL_REPAIR.getCode();
            case SEVENTEEN -> DictEnum.IndustryType.HEALTHCARE.getCode();
            case EIGHTEEN -> DictEnum.IndustryType.RESEARCH_TECHNOLOGY.getCode();
            case NINETEEN -> DictEnum.IndustryType.PUBLIC_MANAGEMENT.getCode();
            default -> DictEnum.IndustryType.OTHER.getCode();
        };
    }

    @Named("toDtoMarriage")
    default String toDtoMarriage(Marriage marriage) {
        if (marriage == null) {
            return null;
        }
        return switch (marriage) {
            case UNMARRIED -> DictEnum.MaritalStatusType.UNMARRIED.getCode();
            case MARRIED -> DictEnum.MaritalStatusType.MARRIED.getCode();
            case DIVORCED -> DictEnum.MaritalStatusType.DIVORCED.getCode();
            case WIDOWED -> DictEnum.MaritalStatusType.WIDOWED.getCode();
            default -> DictEnum.RelationshipType.OTHER.getCode();
        };
    }

    @Named("toDtoEducation")
    default String toDtoEducation(Education education) {
        if (education == null) {
            return null;
        }
        return switch (education) {
            case HIGH_SCHOOL -> DictEnum.Education.HIGH_SCHOOL.getCode();
            case COLLEGE -> DictEnum.Education.UNDERGRADUATE.getCode();
            case JUNIOR_COLLEGE -> DictEnum.Education.COLLEGE.getCode();
            case MASTER, DOCTOR -> DictEnum.Education.POSTGRADUATE.getCode();
            default -> null;
        };
    }

    @Named("toIncomeInfo")
    default String toIncomeInfo(Integer incomeTypeStr) {
        if (incomeTypeStr == null) {
            return DictEnum.MonthlyIncomeType.BELOW_3000.getCode();
        }
        DictEnum.MonthlyIncomeType incomeType = DictEnum.MonthlyIncomeType.getByMoney(incomeTypeStr);
        return incomeType.getCode();
    }

    default List<UserContactDTO> toUserContactInfoListDTOs(List<UserContactInfo> contactInfoList) {
        List<UserContactDTO> list = new ArrayList<>();
        if (contactInfoList.isEmpty()) {
            return list;
        }
        for (UserContactInfo userContactInfo : contactInfoList) {
            UserContactDTO userContactInfoDTO = new UserContactDTO();
            userContactInfoDTO.setName(userContactInfo.getName());
            userContactInfoDTO.setMobile(userContactInfo.getPhone());

            userContactInfoDTO.setUserRelation(toUserRelationship(userContactInfo.getRelation()));
            list.add(userContactInfoDTO);
        }
        return list;
    }

    default String toUserRelationship(Relation relationShip) {
        if (relationShip == null) {
            return DictEnum.RelationshipType.OTHER.getCode();
        }
        return switch (relationShip) {
            case PARENTS -> DictEnum.RelationshipType.PARENTS.getCode();
            case SPOUSE -> DictEnum.RelationshipType.RELATIVES.getCode();
            case SIBLING -> DictEnum.RelationshipType.RELATIVES.getCode();
            case FRIEND -> DictEnum.RelationshipType.FRIEND.getCode();
            case COLLEAGUE -> DictEnum.RelationshipType.COLLEAGUE.getCode();
            case CHILDREN -> DictEnum.RelationshipType.RELATIVES.getCode();
            case RELATIVE -> DictEnum.RelationshipType.RELATIVES.getCode();
            case UNKNOWN -> DictEnum.RelationshipType.OTHER.getCode();
        };
    }

    @Mapping(source = "certOrg", target = "certSignOrg")
    @Mapping(source = "certStartDay", target = "certValidStart", dateFormat = "yyyy.MM.dd")
    @Mapping(source = "req", target = "certValidEnd", qualifiedByName = "certEndCvt")
    @Mapping(source = "facePicPath", target = "headOssKey")
    @Mapping(source = "nationPicPath", target = "nationOssKey")
    @Mapping(source = "gender", target = "gender", qualifiedByName = "genderCvt")
    @Mapping(source = "certNo", target = "provinceCode", qualifiedByName = "provinceCvt")
    @Mapping(source = "certNo", target = "cityCode", qualifiedByName = "cityCvt")
    @Mapping(source = "certNo", target = "districtCode", qualifiedByName = "districtCvt")
    void ocrEditRequestConvert(IdCardInfoSaveRequest req, @MappingTarget UserOcr ocr);

    @Mapping(source = "certSignOrg", target = "certOrg")
    @Mapping(source = "certValidStart", target = "certStartDay", dateFormat = "yyyy.MM.dd")
    @Mapping(source = "certValidEnd", target = "certEndDay", qualifiedByName = "certEndStrCvt")
    @Mapping(source = "headOssKey", target = "facePicUrl")
    @Mapping(source = "nationOssKey", target = "nationPicUrl")
    @Mapping(source = "gender", target = "gender", qualifiedByName = "genderStrCvt")
    IdCardInfoResponse ocrToIdCardInfoResponse(UserOcr ocr);

    @Named("genderCvt")
    default Gender genderCvt(String gender) {
        return switch (gender) {
            case "男" -> Gender.MALE;
            case "女" -> Gender.FEMALE;
            default -> Gender.UNKNOWN;
        };
    }

    @Named("genderStrCvt")
    default String genderStrCvt(Gender gender) {
        return switch (gender) {
            case MALE -> "男";
            case FEMALE -> "女";
            default -> "";
        };
    }

    @Named("certEndCvt")
    default LocalDate certEndCvt(IdCardInfoSaveRequest req) {
        if (req.isPermanent()) {
            return BaseConstants.DEFAULT_LONG_CERT_END;
        }
        return LocalDate.parse(req.getCertEndDay(), DateTimeFormatter.ofPattern("yyyy.MM.dd"));
    }


    @Named("certEndStrCvt")
    default String certEndStrCvt(LocalDate validEndDate) {
        if (validEndDate == null) {
            //返回空
            return null;
        }
        if (validEndDate.isEqual(BaseConstants.DEFAULT_LONG_CERT_END)) {
            return "长期";
        }
        return validEndDate.format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
    }

    @Named("permanent")
    default boolean permanent(LocalDate validEndDate) {
        if (validEndDate == null) {
            //返回空
            return false;
        }
        if (validEndDate.isEqual(BaseConstants.DEFAULT_LONG_CERT_END)) {
            return true;
        }

        return false;
    }



    @Named("provinceCvt")
    default String provinceCvt(String certNo) {
        return certNo.substring(0, 2) + "0000";
    }


    @Named("cityCvt")
    default String cityCvt(String certNo) {
        return certNo.substring(0, CERT_NO_CITY_LENGTH) + "00";
    }


    @Named("districtCvt")
    default String districtCvt(String certNo) {
        return certNo.substring(0, CERT_NO_DISTRICT_LENGTH);
    }


}
