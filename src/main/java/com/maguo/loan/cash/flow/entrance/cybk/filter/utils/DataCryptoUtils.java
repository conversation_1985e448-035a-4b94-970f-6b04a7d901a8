package com.maguo.loan.cash.flow.entrance.cybk.filter.utils;

import com.jinghang.common.util.IdGen;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.crypt.DigestUtil;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CYBKCommonData;
import com.maguo.loan.cash.flow.entrance.cybk.config.CYBKConfig;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CYBKCommonResponse;
import com.maguo.loan.cash.flow.entrance.cybk.exception.CYBKBizException;
import com.maguo.loan.cash.flow.entrance.cybk.enums.CYBKResultCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DataCryptoUtils {

    private static Logger logger = LoggerFactory.getLogger(DataCryptoUtils.class);
    private static final int MAX_ID_LENGTH = 32;

    /**
     * 验签解密
     *
     * @param requestData
     * @param config
     * @return
     */
    public static boolean checkSignAndDecrypt(CYBKCommonData requestData, CYBKConfig config) {
        //返回报文解密
        String decrypted = AESUtil.decrypt(requestData.getJson(), config.getContentKey());
        logger.error("解密后报文：{}", decrypted);

        //验签
        try {
            boolean verify = verify(requestData, decrypted, config);
            if (!verify) {
                logger.error("RSA验签失败");
                throw new CYBKBizException(CYBKResultCode.VERIFY_SIGN_ERROR);
            }
            requestData.setJson(decrypted);
            return true;
        }catch (Exception e){
            logger.error("RSA验签失败", e);
            throw new CYBKBizException(CYBKResultCode.VERIFY_SIGN_ERROR);
        }
    }

    public static boolean verify(CYBKCommonData resp, String decryptedContent, CYBKConfig config) {
        try {
            String decryptedSign = AESUtil.decrypt(resp.getSign(), config.getSignKey());
            String md5 = DigestUtil.md5(decryptedContent);
            return md5.equalsIgnoreCase(decryptedSign);
        } catch (Exception e) {
            logger.error("RSA验签失败", e);
            throw new CYBKBizException(CYBKResultCode.VERIFY_SIGN_ERROR);
        }
    }

    public static CYBKCommonData signAndEncrypt(CYBKCommonData resp, CYBKCommonResponse cybkCommonResponse, CYBKConfig config){
        try {
            String requestStr = JsonUtil.convertToString(cybkCommonResponse);
            String md5 = DigestUtil.md5(requestStr);
            logger.info("长银直连, 加密前请求报文: {}", requestStr);
            logger.info("长银直连, md5: {}", md5);

            // 签名加密
            String encryptSign = AESUtil.encrypt(md5, config.getSignKey());
            // 报文内容加密
            String encrypt = AESUtil.encrypt(requestStr, config.getContentKey());

            resp.setJson(encrypt);
            resp.setSign(encryptSign);
            resp.setRandomStr(IdGen.genId("CYRDM", MAX_ID_LENGTH));
            return resp;
        }catch (Exception e){
            throw new CYBKBizException(CYBKResultCode.SM4_ERROR);
        }
    }
}
