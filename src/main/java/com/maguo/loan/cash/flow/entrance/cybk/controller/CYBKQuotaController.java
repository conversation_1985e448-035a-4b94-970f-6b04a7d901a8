package com.maguo.loan.cash.flow.entrance.cybk.controller;

import com.alibaba.fastjson.JSONObject;
import com.jinghang.capital.api.QuotaService;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.bank.BankResultBackDto;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CYBKCommonRequest;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CYBKCommonResponse;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CommonResult;
import com.maguo.loan.cash.flow.entrance.cybk.dto.quota.res.UpQuotaBackResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/forward/limit/adjust/notice/v2")
public class CYBKQuotaController {


    @Autowired
    private QuotaService quotaService;

    private static Logger logger = LoggerFactory.getLogger(CYBKQuotaController.class);
//    private CYBKConfig config;
//
//    public CYBKPropertyController(CYBKConfig cybkConfig){
//        this.config = cybkConfig;
//    }

    /**
     * 资产正向调额结果通知
     * @param cybkCommonRequest
     * @return
     */
    @PostMapping("/mycjzygs")
    public CYBKCommonResponse upQuotaBack(@RequestBody CYBKCommonRequest cybkCommonRequest) {
        logger.info("资产正向调额结果通知接收参数：{}", JsonUtil.toJsonString(cybkCommonRequest));
        String bodyString = cybkCommonRequest.getBody().toString();
        //组装公共回调报文
        BankResultBackDto bankResultBackDto = new BankResultBackDto();
        bankResultBackDto.setBankChannel(BankChannel.CYBK);
        bankResultBackDto.setJson(bodyString);
        //回调资金系统
        bankResultBackDto = quotaService.upQuotaBack(bankResultBackDto).getData();
        //组装长银响应报文
        CYBKCommonResponse cybkCommonResponse = CommonResult.assembleResponse(cybkCommonRequest, bankResultBackDto.getJson());
        logger.info("资产正向调额结果通知响应参数：{}", JsonUtil.toJsonString(cybkCommonResponse));
        return cybkCommonResponse;
    }
//
//    @GetMapping("/api/property/v1/getTestParam")
//    public void getContract() throws JsonProcessingException {
//        JSONObject resJson = new JSONObject();
//        resJson.put("resultCode","01");
//        resJson.put("resultDesc","成功");
//
//        CYBKRequestHeader requestHeader = new CYBKRequestHeader();
//        requestHeader.setReqNo(IdGen.genId("CY", 32 - 2));
//        requestHeader.setVersion("01");
//        requestHeader.setChannel(config.getChannel());
//        requestHeader.setAppID(config.getAppid());
//        requestHeader.setReqDateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
//
//        CYBKCommonRequest cybkCommonRequest = new CYBKCommonRequest();
//        cybkCommonRequest.setHead(requestHeader);
//        cybkCommonRequest.setBody(resJson);
//
//        String requestStr = JsonUtil.convertToString(cybkCommonRequest);
//        String md5 = DigestUtil.md5(requestStr);
//        logger.info("长银直连, 加密前请求报文: {}", requestStr);
//        logger.info("长银直连, md5: {}", md5);
//
//        // 签名加密
//        String encryptSign = AESUtil.encrypt(md5, config.getSignKey());
//        // 报文内容加密
//        String encrypt = AESUtil.encrypt(requestStr, config.getContentKey());
//
//        CYBKCommonData baseRemoteData = new CYBKCommonData();
//        baseRemoteData.setChannel(config.getChannel());
//        baseRemoteData.setKey("1");
//        baseRemoteData.setJson(encrypt);
//        baseRemoteData.setSign(encryptSign);
//        baseRemoteData.setRandomStr(IdGen.genId("CYRDM", 32));
//
//        logger.info("接口响应参数：{}", JsonUtil.toJsonString(baseRemoteData));
//    }
}
