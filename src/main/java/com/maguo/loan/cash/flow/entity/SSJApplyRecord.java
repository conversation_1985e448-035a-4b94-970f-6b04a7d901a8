package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Entity
@Table(name = "ssj_apply_record")
public class SSJApplyRecord extends BaseEntity {

    @Override
    protected String prefix() {
        return "SAR";
    }

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 借款订单编号
     */
    private String loanNo;
    /**
     * 身份证正面
     */
    private String idPositive;
    /**
     * 身份证反面
     */
    private String idNegative;
    /**
     * 活体照1
     */
    private String livePhoto1;
    /**
     * 活体照2
     */
    private String livePhoto2;
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号
     */
    private String idCardNo;
    /**
     * 身份证有效期
     */
    private String idDueTime;
    /**
     * 身份证地址
     */
    private String idAddress;
    /**
     * 性别
     */
    private String idSex;
    /**
     * 民族
     */
    private String idEthnic;
    /**
     * 身份证签发机构
     */
    private String idIssueOrg;
    /**
     * 活体检测分数
     */
    private String faceScore;
    /**
     * 学历
     */
    private String education;
    /**
     * 职业
     */
    private String job;
    /**
     * 工作单位
     */
    private String workUnitName;
    /**
     * 工作单位地址
     */
    private String workUnitAddress;
    /**
     * 月收入
     */
    private String monthlyIncome;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 借款用途
     */
    private String loanPurpose;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 紧急联系人关系
     */
    private String relations;
    /**
     * 设备信息
     */
    private String deviceInfo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getIdPositive() {
        return idPositive;
    }

    public void setIdPositive(String idPositive) {
        this.idPositive = idPositive;
    }

    public String getIdNegative() {
        return idNegative;
    }

    public void setIdNegative(String idNegative) {
        this.idNegative = idNegative;
    }

    public String getLivePhoto1() {
        return livePhoto1;
    }

    public void setLivePhoto1(String livePhoto1) {
        this.livePhoto1 = livePhoto1;
    }

    public String getLivePhoto2() {
        return livePhoto2;
    }

    public void setLivePhoto2(String livePhoto2) {
        this.livePhoto2 = livePhoto2;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getIdDueTime() {
        return idDueTime;
    }

    public void setIdDueTime(String idDueTime) {
        this.idDueTime = idDueTime;
    }

    public String getIdAddress() {
        return idAddress;
    }

    public void setIdAddress(String idAddress) {
        this.idAddress = idAddress;
    }

    public String getIdSex() {
        return idSex;
    }

    public void setIdSex(String idSex) {
        this.idSex = idSex;
    }

    public String getIdEthnic() {
        return idEthnic;
    }

    public void setIdEthnic(String idEthnic) {
        this.idEthnic = idEthnic;
    }

    public String getIdIssueOrg() {
        return idIssueOrg;
    }

    public void setIdIssueOrg(String idIssueOrg) {
        this.idIssueOrg = idIssueOrg;
    }

    public String getFaceScore() {
        return faceScore;
    }

    public void setFaceScore(String faceScore) {
        this.faceScore = faceScore;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getWorkUnitName() {
        return workUnitName;
    }

    public void setWorkUnitName(String workUnitName) {
        this.workUnitName = workUnitName;
    }

    public String getWorkUnitAddress() {
        return workUnitAddress;
    }

    public void setWorkUnitAddress(String workUnitAddress) {
        this.workUnitAddress = workUnitAddress;
    }

    public String getMonthlyIncome() {
        return monthlyIncome;
    }

    public void setMonthlyIncome(String monthlyIncome) {
        this.monthlyIncome = monthlyIncome;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(String loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getRelations() {
        return relations;
    }

    public void setRelations(String relations) {
        this.relations = relations;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }
}
