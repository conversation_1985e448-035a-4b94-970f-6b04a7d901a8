package com.sing.service.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sing.service.entity.SignAccount;
import com.sing.service.entity.SignContract;
import com.sing.service.entity.SignContractLog;
import com.sing.service.entity.dto.ESignDTO;
import com.sing.service.entity.vo.UploadDataVo;
import com.sing.service.error.EsignDemoException;
import com.sing.service.response.ResultCode;
import com.sing.service.response.ResultMsg;
import com.sing.service.service.ESignService;
import com.sing.service.service.SignAccountService;
import com.sing.service.service.SignContractService;
import com.sing.service.service.SigncontractLogService;
import com.sing.service.util.FileUtils;
import com.sing.service.util.UUIDUtils;

import com.sing.service.util.enums.TrafficSignalEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.service.impl.EsignServiceImpl
 * @作者 Mr.sandman
 * @时间 2025/05/22 14:20
 */
@Service
public class ESignServiceImpl implements ESignService {

  private static final Logger LOGGER = LoggerFactory.getLogger(ESignServiceImpl.class);

  @Autowired
  private SignAccountService signAccountService;
  @Autowired
  private SignContractService signContractService;
  @Autowired
  private SigncontractLogService signcontractLogService;

  @Autowired
  private RedisTemplate<String, String> redisTemplate;

  @Autowired
  private TransactionTemplate transactionTemplate;

  // Redis键配置
  @Value("${esign.redis.queue.key:esign:request:queue}")
  private String esignQueueKey;

  @Value("${esign.redis.processing.key.prefix:esign:processing:}")
  private String esignProcessingKeyPrefix;

  @Value("${esign.redis.rate.limit.key.prefix:esign:rate:limit:}")
  private String rateLimitKeyPrefix;

  // 超时配置
  @Value("${esign.timeout.queue:30000}")
  private long queueTimeout;

  @Value("${esign.timeout.processing:10000}")
  private long processingTimeout;

  @Value("${esign.timeout.rate.window:1000}")
  private long rateLimitWindow;

  // 限流配置
  @Value("${esign.rate.limit:50}")
  private int rateLimit;

  @Value("${esign.queue.max.size:1000}")
  private int maxQueueSize;


  /**
   * 签章
   * @param eSignDTO 签章参数
   * @return ResultMsg 签章后的结果
   */
  @Override
  public ResultMsg sign(ESignDTO eSignDTO) throws EsignDemoException {
    LOGGER.info("开始处理签章请求参数: {}", JSONUtil.toJsonStr(eSignDTO));
    String cardNo = eSignDTO.getAuthDto().getCardNo();
    String contractCode = eSignDTO.getContractCode();
    String taskId = UUID.randomUUID().toString();
    long startTime = System.currentTimeMillis();

    // 1. 原子化限流检查
    String rateLimitKey = rateLimitKeyPrefix + (System.currentTimeMillis() / 1000);
    Long currentCount = redisTemplate.execute(
        new DefaultRedisScript<>(
            "local current = redis.call('incr', KEYS[1])\n" +
                "if current == 1 then\n" +
                "    redis.call('pexpire', KEYS[1], ARGV[1])\n" +
                "end\n" +
                "return current",
            Long.class
        ),
        Collections.singletonList(rateLimitKey),
        String.valueOf(rateLimitWindow + 500)); // 1.5秒过期，防止边界问题

    // 2. 队列控制
    if (currentCount != null && currentCount > rateLimit) {
      // 检查队列长度
      Long queueSize = redisTemplate.opsForList().size(esignQueueKey);
      if (queueSize != null && queueSize >= maxQueueSize) {
        LOGGER.warn("请求队列已满，拒绝处理 taskId={}", taskId);
        throw new EsignDemoException("系统繁忙，请稍后再试");
      }

      // 加入队列并获取位置
      Long queuePosition = redisTemplate.opsForList().rightPush(esignQueueKey, taskId);
      LOGGER.info("请求已加入队列，taskId={}, 排队位置={}, 合同编号={}, 身份证号={}",
                  taskId, queuePosition, contractCode, cardNo);

      try {
        // 3. 队列等待处理
        while (true) {
          String headTask = (String) redisTemplate.opsForList().index(esignQueueKey, 0);
          if (taskId.equals(headTask)) {
            break;
          }

          // 检查超时
          if (System.currentTimeMillis() - startTime > queueTimeout) {
            redisTemplate.opsForList().remove(esignQueueKey, 0, taskId);
            LOGGER.warn("排队超时，任务被移除, taskId={}", taskId);
            throw new EsignDemoException("排队超时，请稍后重试");
          }

          Thread.sleep(200); // 适当休眠降低Redis压力
        }

        // 4. 获取处理权
        return processTask(eSignDTO, taskId, contractCode, cardNo);
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        redisTemplate.opsForList().remove(esignQueueKey, 0, taskId);
        LOGGER.error("签章请求被中断，taskId={}", taskId, e);
        throw new EsignDemoException("处理被中断", e);
      }
    } else {
      // 5. 直接处理
      LOGGER.info("请求直接处理中，taskId={}, 合同编号={}, 身份证号={}",
                  taskId, contractCode, cardNo);
      return processTask(eSignDTO, taskId, contractCode, cardNo);
    }
  }


  private ResultMsg processTask(ESignDTO eSignDTO, String taskId, String contractCode, String cardNo)
      throws EsignDemoException {
    LOGGER.info("处理任务开始，taskId={}, 合同编号={}, 身份证号={}", taskId, contractCode, cardNo);
    String processingKey = esignProcessingKeyPrefix + taskId;
    try {
      // 获取处理锁
      boolean acquired = Boolean.TRUE.equals(
          redisTemplate.opsForValue().setIfAbsent(
              processingKey,
              "true",
              processingTimeout,
              TimeUnit.MILLISECONDS));

      if (!acquired) {
        LOGGER.warn("Processing lock not acquired, taskId={}", taskId);
        throw new EsignDemoException("系统繁忙，请稍后再试");
      }

      // 如果是队列任务，从队列移除
      if (redisTemplate.opsForList().remove(esignQueueKey, 0, taskId) != null) {
        LOGGER.debug("Task removed from queue, taskId={}", taskId);
      }

      // 执行实际业务逻辑
      return executeWithLock(eSignDTO, contractCode, cardNo);
    } finally {
      try {
        redisTemplate.delete(processingKey);
      } catch (Exception e) {
        LOGGER.error("Failed to release processing lock, taskId={}", taskId, e);
      }
    }
  }

  // 以下executeWithLock和releaseLockWithRetry方法保持不变
  private ResultMsg executeWithLock(ESignDTO eSignDTO, String contractCode, String cardNo)
      throws EsignDemoException {
    LOGGER.info("开始处理签章任务，合同编号={}, 身份证号={}", contractCode, cardNo);
    String lockKey = "lock:sign:" + contractCode + ":cardNo:" + cardNo;
    Boolean isLocked = false;
    int maxRetries = 3;

    try {
      // 带重试的锁获取
      for (int i = 0; i < maxRetries; i++) {
        isLocked = Boolean.TRUE.equals(redisTemplate.opsForValue()
                                           .setIfAbsent(lockKey, "locked", 3, TimeUnit.MINUTES));
        if (isLocked) break;

        LOGGER.warn("获取锁失败（第{}次尝试）key=[{}]", i + 1, lockKey);
        if (i < maxRetries - 1) {
          Thread.sleep(500);
        }
      }

      if (isLocked) {
        LOGGER.info("成功获取锁 key=[{}]", lockKey);
        transactionTemplate.setTimeout(120); // 2分钟
        return transactionTemplate.execute(status -> {
          try {
            return doSignLogic(eSignDTO);
          } catch (EsignDemoException e) {
            status.setRollbackOnly();
            throw new RuntimeException(e);
          }
        });
      } else {
        throw new EsignDemoException("系统繁忙，请稍后再试");
      }
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      throw new EsignDemoException("操作被中断", e);
    } finally {
      if (Boolean.TRUE.equals(isLocked)) {
        releaseLockWithRetry(lockKey);
      }
    }
  }

  private void releaseLockWithRetry(String lockKey) {
    int maxRetries = 3;
    for (int i = 0; i < maxRetries; i++) {
      try {
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        redisTemplate.execute(new DefaultRedisScript<>(script, Boolean.class),
                              Collections.singletonList(lockKey), "locked");
        LOGGER.info("已释放锁 key=[{}]", lockKey);
        break;
      } catch (Exception e) {
        LOGGER.error("释放锁失败（第{}次重试）key=[{}]", i + 1, lockKey, e);
        if (i == maxRetries - 1) {
          LOGGER.error("释放锁失败，达到最大重试次数 key=[{}]", lockKey, e);
        }
        try {
          Thread.sleep(100);
        } catch (InterruptedException ex) {
          Thread.currentThread().interrupt();
          LOGGER.warn("锁重试休眠被中断 key=[{}]", lockKey, e);
        }
      }
    }
  }


  private ResultMsg doSignLogic(ESignDTO eSignDTO) throws EsignDemoException {
    LOGGER.info("开始处理签章逻辑，请求参数:{}", JSONUtil.toJsonStr(eSignDTO));
    ResultMsg resultMsg = new ResultMsg();

    SignAccount signAccount = signAccountService.getOne(new QueryWrapper<SignAccount>()
                                                            .eq("card_no", eSignDTO.getAuthDto().getCardNo()).last("LIMIT 1"));
    LOGGER.info("查询到的个人账号信息:{}", JSONUtil.toJsonStr(signAccount));
    if (signAccount == null) {
      // 身份证照片地址
      String portraitUrl = signAccountService.getOssUrl(eSignDTO.getAuthDto().getCardPortrait());
      String backUrl = signAccountService.getOssUrl(eSignDTO.getAuthDto().getCardNationalEmblem());
      String faceUrl = signAccountService.getOssUrl(eSignDTO.getAuthDto().getFace().getFaceImage());

      File portraitFile = null, backFile = null, faceFile = null;

      try {
        portraitFile = FileUtils.downloadToLocal(portraitUrl);
        backFile = FileUtils.downloadToLocal(backUrl);
        faceFile = FileUtils.downloadToLocal(faceUrl);

        UploadDataVo portraitUpload = signAccountService.getUploadUrl(portraitFile.getAbsolutePath());
        signAccountService.uploadFileStrem(portraitUpload.getUploadUrl(), portraitFile.getAbsolutePath());
        LOGGER.info("上传身份证正面照状态成功");

        UploadDataVo backUpload = signAccountService.getUploadUrl(backFile.getAbsolutePath());
        signAccountService.uploadFileStrem(backUpload.getUploadUrl(), backFile.getAbsolutePath());
        LOGGER.info("上传身份证反面照状态成功");

        UploadDataVo faceUpload = signAccountService.getUploadUrl(faceFile.getAbsolutePath());
        signAccountService.uploadFileStrem(faceUpload.getUploadUrl(), faceFile.getAbsolutePath());
        LOGGER.info("上传身份证人脸照状态成功");


        String eviPointId = signAccountService.eviPoint(eSignDTO.getAuthDto());
        LOGGER.info("证据点ID: {}", eviPointId);

        SignAccount newAccount = new SignAccount();
        newAccount.setCardNo(eSignDTO.getAuthDto().getCardNo());
        newAccount.setEviPointId(eviPointId);
        newAccount.setCreditId(eSignDTO.getCreditId());
        signAccountService.save(newAccount);

        String accountId = signAccountService.addPersonAccount(
            eSignDTO.getAuthDto().getName(), eSignDTO.getAuthDto().getCardNo(), eviPointId);
        newAccount.setAccountId(accountId);
        signAccountService.updateById(newAccount);

        String sealData = signAccountService.createPersonalSeal(accountId);
        newAccount.setSealData(sealData);
        signAccountService.updateById(newAccount);

        signAccount = newAccount; // 后续继续用这个账户
      } catch (IOException e) {
        throw new EsignDemoException("下载或上传文件失败: " + e.getMessage());
      } finally {
        FileUtils.safeDeleteFile(portraitFile);
        FileUtils.safeDeleteFile(backFile);
        FileUtils.safeDeleteFile(faceFile);
      }
    }

    // 合同签署逻辑，加 try-catch
    String contractNo = UUIDUtils.generateContractUUID();
    byte[] signedPdf;

    try {
      signedPdf = signContract(signAccount, eSignDTO, contractNo);
    } catch (Exception e) {
      LOGGER.error("调用签章服务失败: {}", e.getMessage(), e);
      throw new EsignDemoException("签署合同失败，请稍后重试");
    }

    if (signedPdf != null) {
      String ossKey = signAccountService.uploadFile(new ByteArrayInputStream(signedPdf), signedPdf.length, true);

      SignContract contractObj = signContractService.getOne(new QueryWrapper<SignContract>()
                                                              .eq("contract_code", eSignDTO.getContractCode())
                                                              .eq("traffic_code", eSignDTO.getTrafficCode()));

      SignContractLog contractLog = new SignContractLog();
      contractLog.setContractId(contractObj.getId());
      contractLog.setContractNo(contractNo);
      contractLog.setContractName(contractObj.getContractName());
      contractLog.setContractUrl(ossKey);
      contractLog.setCreateTime(new Date());
      contractLog.setCreditId(eSignDTO.getCreditId());
      signcontractLogService.save(contractLog);

      resultMsg.setResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), contractNo);
    } else {
      throw new EsignDemoException("合同签章失败：返回PDF为空");
    }

    return resultMsg;
  }


  /**
   * 获取签章链接
   * @param contractNo 签章合同编号
   * @return ResultMsg 签章链接
   */
  @Override
  public ResultMsg getSignUrl( String contractNo ) {
    ResultMsg resultMsg = new ResultMsg();
    if ( StringUtils.isNotBlank(contractNo) )  {
      SignContractLog signContractLog = signcontractLogService.getOne(new LambdaQueryWrapper<SignContractLog>()
                                                                          .eq(SignContractLog :: getContractNo, contractNo));
      if ( signContractLog != null ) {
        String signUrl = signContractLog.getContractUrl();
        if ( StringUtils.isNotBlank(signUrl) ) {
          resultMsg.setResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), signUrl);
        }
      }
    } else {
      resultMsg.setResult(ResultCode.CONTRACT_NOT_EXIST.getCode(), ResultCode.CONTRACT_NOT_EXIST.getMessage());
    }
    return resultMsg;
  }

  /**
   * 根据关键字获取签章位置
   * @param keyWords 关键字
   */
  @Override
  public ResultMsg getDocKeyPosition( MultipartFile file, String keyWords ) throws EsignDemoException {
    ResultMsg resultMsg = new ResultMsg();
    File tempFile = null;
    try {
      tempFile = File.createTempFile("upload-", ".pdf");
      file.transferTo(tempFile);
      String filePath = tempFile.getAbsolutePath();
      List docKeyPosition = signAccountService.getDocKeyPosition(filePath, keyWords);
      resultMsg.setResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), docKeyPosition);
    } catch (IOException e) {
      throw new EsignDemoException("文件处理失败");
    } finally {
      if (tempFile != null && tempFile.exists()) {
        tempFile.delete();
      }
    }
    return resultMsg;
  }

  /**
   * 上传文件到oss获取
   * @param file 文件
   */
  @Override
  public ResultMsg uploadFileToOss( MultipartFile file ) throws EsignDemoException {
    ResultMsg resultMsg = new ResultMsg();
    try {
      String ossKey;
      // 判断是否是 PDF 文件
      if (file.getOriginalFilename().toLowerCase().endsWith(".pdf")) {
         ossKey = signAccountService.uploadFile(file.getInputStream(), file.getSize(),  true);
      } else {
         ossKey = signAccountService.uploadFile(file.getInputStream(), file.getSize(),  false);
      }
      resultMsg.setResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), ossKey);
    } catch (IOException e) {
      throw new EsignDemoException("文件处理失败");
    }
    return resultMsg;
  }

  /**
   * 填充pdf文件模版
   * @param file 文件
   */
  /**
   * 填充pdf文件模版并保存到本地路径
   * @param file 上传的PDF模板文件
   */
  @Override
  public ResultMsg createFileFromTemplate(MultipartFile file) throws EsignDemoException {
    ResultMsg resultMsg = new ResultMsg();

    try {
      // 获取PDF填充信息
      Map<String, Object> pdfFillInfo = getPdfFillInfoTest();

      // 读取上传文件的字节流
      byte[] templateBytes = file.getBytes();

      // 调用模板填充方法生成新的PDF字节流
      byte[] filledPdfBytes = signAccountService.createFileFromTemplate(templateBytes, file.getOriginalFilename(), pdfFillInfo);

      // 定义本地保存路径（例如：/data/pdfs/）
      String localSavePath = "/Users/<USER>/Downloads/data/";
      File saveDir = new File(localSavePath);
      if (!saveDir.exists()) {
        saveDir.mkdirs(); // 创建目录
      }

      // 生成唯一文件名，防止覆盖
      String outputFileName = "filled_" + System.currentTimeMillis() + ".pdf";
      File outputFile = new File(saveDir, outputFileName);

      // 写入本地文件
      try (FileOutputStream fos = new FileOutputStream(outputFile)) {
        fos.write(filledPdfBytes);
      }

      // 返回本地存储路径或文件名
      resultMsg.setResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), outputFileName);

    } catch (IOException e) {
      throw new EsignDemoException("文件处理失败: " + e.getMessage());
    }

    return resultMsg;
  }

  /**
   * 获取oss文件url
   *
   * @param ossKey
   */
  @Override
  public ResultMsg getOssUrl( String ossKey ) throws EsignDemoException {
    ResultMsg resultMsg = new ResultMsg();
    String ossUrl = signAccountService.getOssUrl(ossKey);
    resultMsg.setResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), ossUrl);
    return resultMsg;
  }

  /**
   * 保存合同表内容
   *
   * @param contractDTO
   */
  @Override
  public ResultMsg saveContract( SignContract contractDTO ) {
    signContractService.save(contractDTO);
    return new ResultMsg(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage());
  }


  /**
   * 签名合同 公共方法
   */
  private byte[] signContract(SignAccount signAccount, ESignDTO eSignDTO, String contractNo) throws IOException, EsignDemoException {
    SignContract contractObj;
    if ( eSignDTO.getTrafficCode() != null ) {
      // 获取合同信息
      contractObj = signContractService.getOne(new QueryWrapper<SignContract>()
                                                                .eq("contract_code", eSignDTO.getContractCode())
                                                                .eq("traffic_code", eSignDTO.getTrafficCode()));
    } else {
      // 获取合同信息
      contractObj = signContractService.getOne(new QueryWrapper<SignContract>()
                                                                .eq("contract_code", eSignDTO.getContractCode()));
    }

    LOGGER.info("合同对象: {}", contractObj);

    String contractUrl = signAccountService.getOssUrl(contractObj.getContractUrl());
    LOGGER.info("合同地址: {}", contractUrl);

    File pdfFile = FileUtils.downloadPdfToLocal(contractUrl);
    LOGGER.info("合同文件: {}", pdfFile);

    try {
      Map<String, Object> pdfFillInfo = getPdfFillInfo(eSignDTO, contractNo);
      byte[] filledPdf = signAccountService.createFileFromTemplate(
          FileUtil.readBytes(pdfFile), pdfFile.getName(), pdfFillInfo);

      byte[] signedPdf = null;
      if (contractObj.getSignType() == 1) {
        signedPdf = signAccountService.personSign(signAccount, contractObj, pdfFile.getName(), filledPdf);
      } else if (contractObj.getSignType() == 2) {
        signedPdf = signAccountService.platformSign(signAccount, contractObj, pdfFile.getName(), filledPdf);
      } else if (contractObj.getSignType() == 3) {
        byte[] personSigned = signAccountService.personSign(signAccount, contractObj, pdfFile.getName(), filledPdf);
        signedPdf = signAccountService.platformSign(signAccount, contractObj, pdfFile.getName(), personSigned);
      }

      return signedPdf;
    } finally {
      FileUtils.safeDeleteFile(pdfFile);
    }
  }



  // 先封装部分pdf填充信息 后续再补充
  protected Map<String, Object> getPdfFillInfo( ESignDTO eSignDTO, String contractNo) {
    //填充信息
    LocalDate currentDate = LocalDate.now();
    Map<String, Object> txtFields = new HashMap<>();// 模板中包含待填充文本域时，文本域Key-Value组合
    txtFields.put("cardNo", eSignDTO.getAuthDto().getCardNo());
    txtFields.put("name", eSignDTO.getAuthDto().getName());
    txtFields.put("address", eSignDTO.getAddress());
    txtFields.put("phone", eSignDTO.getPhone());
    txtFields.put("city", extractCityFromAddress(eSignDTO.getAddress()));
    txtFields.put("amountNo", eSignDTO.getAmountNo());
    txtFields.put("stagesNumber", eSignDTO.getStagesNumber());
    txtFields.put("bankCardNo", eSignDTO.getBankCardNo());
    txtFields.put("bankName", eSignDTO.getBankName());
    txtFields.put("amount", eSignDTO.getAmount());
    txtFields.put("contractNo", contractNo);
    txtFields.put("authNo", eSignDTO.getAuthNo());
    txtFields.put("year", currentDate.getYear());
    txtFields.put("mon", currentDate.getMonth().getValue());
    txtFields.put("day", currentDate.getDayOfMonth());
    txtFields.put("date", currentDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
    // 新添加一部分数据
    txtFields.put("purpose", eSignDTO.getPurpose());
    txtFields.put("capitalRmb", eSignDTO.getCapitalRmb());
    txtFields.put("lowercaseRmb", eSignDTO.getLowercaseRmb());
    txtFields.put("loanActvYear", eSignDTO.getLoanActvYear());
    txtFields.put("loanActvMonth", eSignDTO.getLoanActvMonth());
    txtFields.put("loanActvDay", eSignDTO.getLoanActvDay());
    txtFields.put("dueYear", eSignDTO.getDueYear());
    txtFields.put("dueMonth", eSignDTO.getDueMonth());
    txtFields.put("dueDay", eSignDTO.getDueDay());
    txtFields.put("intRate", eSignDTO.getIntRate());
    txtFields.put("priceRate", eSignDTO.getPriceRate());
    txtFields.put("mtdCde", eSignDTO.getMtdCde());
    txtFields.put("repayDate", eSignDTO.getRepayDate());
    txtFields.put("repayAmount", eSignDTO.getRepayAmount());
    txtFields.put("guarOdIntRate", eSignDTO.getGuarOdIntRate());
    txtFields.put("acctName", eSignDTO.getAcctName());
    txtFields.put("serviceFee", eSignDTO.getServiceFee());

    return txtFields;
  }

  // 先封装部分pdf填充信息 测试
  private Map<String, Object> getPdfFillInfoTest() {
    //填充信息
    LocalDate currentDate = LocalDate.now();
    Map<String, Object> txtFields = new HashMap<>();// 模板中包含待填充文本域时，文本域Key-Value组合
    txtFields.put("cardNo", "3422221997023034527");
    txtFields.put("name", "张三");
    txtFields.put("address", "上海市松江区九亭镇河图公园B栋");
    txtFields.put("phone", "*********");
    txtFields.put("city", "西安");
    txtFields.put("amountNo", "123");
    txtFields.put("stagesNumber", "12");
    txtFields.put("amount", "200000");
    txtFields.put("bankCardNo", "*****************");
    txtFields.put("bankName", "上海招商银行松江支行");
    txtFields.put("contractNo",UUIDUtils.generateContractUUID());
    txtFields.put("year", currentDate.getYear());
    txtFields.put("mon", currentDate.getMonth().getValue());
    txtFields.put("day", currentDate.getDayOfMonth());
    txtFields.put("date", currentDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
    txtFields.put("purpose", "开公司");
    txtFields.put("capitalRmb", "一万");
    txtFields.put("lowercaseRmb", "10000");
    txtFields.put("loanActvYear", "2023");
    txtFields.put("loanActvMonth", "1");
    txtFields.put("loanActvDay", "1");
    txtFields.put("dueYear", "2025");
    txtFields.put("dueMonth", "12");
    txtFields.put("dueDay", "31");
    txtFields.put("intRate", "12%");
    txtFields.put("priceRate", "30");
    txtFields.put("mtdCde", "等额本息");
    txtFields.put("repayDate", "15");
    txtFields.put("repayAmount", "100");
    txtFields.put("guarOdIntRate", "0.2%");
    txtFields.put("acctName", "招商银行");
    txtFields.put("serviceFee", "100");
    txtFields.put("authNo", "11111");
    return txtFields;
  }

  public static String extractCityFromAddress(String address) {
    if (address == null) return "";

    // 匹配 "省" 后面到 "市" 的内容（包括“市”）
    Pattern pattern = Pattern.compile("省(.*?市)");
    Matcher matcher = pattern.matcher(address);

    if (matcher.find()) {
      return matcher.group(1); // 提取匹配到的城市部分
    }

    // 对于直辖市（如北京市）或没有“省”字样的情况，也尝试直接匹配如 “北京市”
    Pattern directCityPattern = Pattern.compile("^(.*?市)");
    Matcher matcher2 = directCityPattern.matcher(address);
    if (matcher2.find()) {
      return matcher2.group(1);
    }

    return "";
  }

}
