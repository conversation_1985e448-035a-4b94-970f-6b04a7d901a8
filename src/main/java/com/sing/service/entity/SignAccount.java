package com.sing.service.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * (SignAccount)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-20 18:52:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("SignAccount对象")
public class SignAccount {

  @TableId(type = IdType.AUTO)
  @ApiModelProperty("主键id")
  private Long id;

  @ApiModelProperty("证据点id")
  private String eviPointId;

  @ApiModelProperty("个人账户id")
  private String accountId;

  @ApiModelProperty("印章编码")
  private String sealData;

  @ApiModelProperty("身份证号")
  private String cardNo;

  @ApiModelProperty("授信人id")
  private String creditId;

  @ApiModelProperty("创建时间")
  @TableField(fill = FieldFill.INSERT)
  private Date createTime;

  @ApiModelProperty("是否删除 0 未删 1 已删")
  private Integer deleted;

}

