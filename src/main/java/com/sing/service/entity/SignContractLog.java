package com.sing.service.entity;



import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * {@code @公司} 中数金智(上海)有限公司
 * {@code @包名} com.singservice.sing_service.entity.SignContractLog
 * @作者 Mr.sandman
 * @时间 2025/05/20 14:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("SignContractLog对象")
public class SignContractLog {

  @TableId(type = IdType.AUTO)
  @ApiModelProperty("主键id")
  private Long id;

  @ApiModelProperty("合同id")
  private Long contractId;

  @ApiModelProperty("合同编号")
  private String contractNo;

  @ApiModelProperty("认证id")
  private String creditId;

  @ApiModelProperty("身份证号")
  private String cardNo;

  @ApiModelProperty("协议编码")
  private String contractCode;

  @ApiModelProperty("流量标识")
  private String trafficCode;

  @ApiModelProperty("协议名称")
  private String contractName;

  @ApiModelProperty("协议地址")
  private String contractUrl;

  @ApiModelProperty("创建时间")
  @TableField(fill = FieldFill.INSERT)
  private Date createTime;

  @ApiModelProperty("是否删除 0 未删 1 已删")
  private Integer deleted;

}
