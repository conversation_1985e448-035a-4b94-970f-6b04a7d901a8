package com.sing.service;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@EnableApolloConfig
@ComponentScan("com.sing.service.*")
@MapperScan("com.sing.service.dao")
@EnableTransactionManagement
public class SingServiceApplication {

  public static void main(String[] args) {
    SpringApplication.run(SingServiceApplication.class, args);
  }

}
