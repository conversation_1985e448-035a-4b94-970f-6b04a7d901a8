package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.repay.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface RepayService {

    @PostMapping("trial")
    RestResult<TrailResultDto> trial(@RequestBody RepayTrailDto trailDto);

    @PostMapping("apply")
    RestResult<RepayResultDto> repay(@RequestBody RepayApplyDto repayApply);

    @PostMapping("query")
    RestResult<RepayResultDto> queryResult(@RequestBody RepayQueryDto repayQuery);

    @PostMapping("plan/query")
    RestResult<PlanDto> queryPlan(@RequestBody PlanQueryDto planQuery);

    /**
     * 查询逾期还款计划
     *
     * @param overduePlanQueryDto
     * @return
     */
    @PostMapping("overduePlan/query")
    RestResult<OverduePlanDto> queryOverduePlan(@RequestBody OverduePlanQueryDto overduePlanQueryDto);

    @PostMapping("compensated/repay")
    RestResult<CompensatedRepaySyncRltDto> compensatedRepaySync(@RequestBody CompensatedRepaySyncDto repaySyncDto);

    /**
     * 标记资方代偿状态
     *
     * @param claimMarkApplyDto 代偿标记申请
     * @return 结果
     */
    @PostMapping("claim/mark")
    RestResult<ClaimMarkResultDto> claimMark(@RequestBody ClaimMarkApplyDto claimMarkApplyDto);

    /**
     * 资方挪息
     *
     * @param repayDateApplyDto 资方挪息申请
     * @return 结果
     */
    @PostMapping("appropriation/interest")
    RestResult<RepayDateResultDto> appropriationInterest(@RequestBody RepayDateApplyDto repayDateApplyDto);

    /**
     * 代偿重试
     *
     * @param claimRetryDto 代偿重试
     * @return 结果
     */
    @PostMapping("claim/retry")
    RestResult<ClaimRetryResultDto> claimRetry(@RequestBody ClaimRetryDto claimRetryDto);

    /**
     * 还款款结果通知
     *
     * @param repayNoticeDto
     * @return
     */
    @PostMapping("notice")
    RestResult<RepayNoticeResultDto> repayNotice(@RequestBody RepayNoticeDto repayNoticeDto);

    /**
     * 主动发起代偿
     *
     * @param activeLaunchClaimApplyDto 主动发起代偿申请
     * @return 结果
     */
    @PostMapping("claim/active")
    RestResult<ActiveLaunchClaimResultDto> activeLaunchClaim(@RequestBody ActiveLaunchClaimApplyDto activeLaunchClaimApplyDto);

    /**
     * 批量还款申请
     * @param repayApply
     * @return
     */
    @PostMapping("batchApply")
    RestResult<RepayResultDto> batchApply(@RequestBody RepayDeductionApplyDto repayApply);

    /**
     * 批量还款查询
     */
    @PostMapping("batchQuery")
    RestResult<RepayBatchResultDto> batchQuery(@RequestBody RepayQueryDto repayQuery);

    /**
     * 批量还款试算
     */
    @PostMapping("batchTrial")
    RestResult<BatchTrialResultDto> batchTrial(@RequestBody BatchTrailDto trailDto);

    /**
     * 信托计划额度释放
     */
    @PostMapping("trustPlanRelease")
    RestResult<Void> trustPlanRelease(@RequestBody TrustPlanReleaseDto releaseDto);

    /**
     * 代付
     */
    @PostMapping("defray")
    RestResult<DefrayResultDto> defray(@RequestBody DefrayDto defrayDto);

    /**
     * 标记资方代还
     *
     * @param substituteMarkApplyDto 代还标记申请
     * @return 结果
     */
    @PostMapping("substitute/mark")
    RestResult<SubstituteMarkResultDto> substituteMark(@RequestBody SubstituteMarkApplyDto substituteMarkApplyDto);

    /**
     * 代还申请
     *
     * @param substituteApplyDto 代还申请
     * @return 结果
     */
    @PostMapping("substitute/apply")
    RestResult<SubstituteApplyResultDto> substituteApply(@RequestBody SubstituteApplyDto substituteApplyDto);

    /**
     * 手动代还申请
     *
     * @param substituteApplyDto 代还申请
     * @return 结果
     */
    @PostMapping("substitute/hand")
    RestResult<SubstituteApplyResultDto> handSubstituteApply(@RequestBody SubstituteApplyDto substituteApplyDto);


    /**
     * 线下还款回盘文件上传sftp
     */
    @PostMapping("offlineReturnFile/upload")
    RestResult<RepayReturnUploadResultDto> repayOfflineReturnFileUpload(@RequestBody RepayReturnUploadDto uploadDto);
}
