package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.credit.CreditApplyDto;
import com.jinghang.capital.api.dto.credit.CreditQueryDto;
import com.jinghang.capital.api.dto.credit.CreditResultDto;
import com.jinghang.capital.api.dto.credit.ExtInfoDto;
import com.jinghang.capital.api.dto.credit.PreCreditApplyDto;
import com.jinghang.capital.api.dto.credit.PreCreditApplyResultDto;
import com.jinghang.capital.api.dto.credit.RecreditApplyDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface CreditService {

    @PostMapping("preApply")
    RestResult<PreCreditApplyResultDto> preCredit(@RequestBody PreCreditApplyDto preCreditApply);

    @PostMapping("apply")
    RestResult<CreditResultDto> credit(@RequestBody CreditApplyDto<ExtInfoDto> creditApply);

    @PostMapping("recredit")
    RestResult<CreditResultDto> recredit(@RequestBody RecreditApplyDto recreditApply);

    @PostMapping("query")
    RestResult<CreditResultDto> queryResult(@RequestBody CreditQueryDto creditQuery);

    /**
     * 失败的授信, 通知core
     * @param creditApply 授信申请记录dto
     * @return
     */
    @PostMapping("failedNotify")
    RestResult<CreditResultDto> failedNotify(@RequestBody CreditApplyDto<ExtInfoDto> creditApply);

}
