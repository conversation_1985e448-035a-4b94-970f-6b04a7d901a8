<template>
  <div class="app-container">
    <el-form
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item
        label=""
        prop="mobile"
        width="150px"
      >
        <el-input
          v-model="queryParams.mobile"
          placeholder="手机号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item
        prop="rightsSupplier"
        width="150px"
      >
        <el-input
          v-model="queryParams.rightsSupplier"
          placeholder="权益商编号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item
        prop="auditState"
        width="150px"
      >
        <el-select
          v-model="queryParams.auditState"
          placeholder="审核状态"
          clearable
        >
          <el-option
            label="待审核"
            value="AUDITING"
          />
          <el-option
            label="审核通过"
            value="PASS"
          />
          <el-option
            label="审核拒绝"
            value="REJECT"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订阅时间">
        <el-date-picker
          v-model="subscribeDateRange"
          size="small"
          type="datetimerange"
          style="width: 200px"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker
          v-model="unsubscribeDateRange"
          size="small"
          type="datetimerange"
          style="width: 200px"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item label="审批时间">
        <el-date-picker
          v-model="auditDateRange"
          size="small"
          type="datetimerange"
          style="width: 200px"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          round
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button
          round
          size="mini"
          @click="handleResetQuery"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      border="border"
      :data="dataSource"
      style="width: 100%"
    >
      <el-table-column
        label="申请ID"
        prop="id"
        align="center"
      />
      <el-table-column
        label="姓名"
        prop="name"
        align="center"
      />
      <el-table-column
        label="手机号"
        prop="mobile"
        align="center"
      />
      <el-table-column
        label="会员供应商"
        prop="rightsSupplier"
        align="center"
      />
      <el-table-column
        label="权益包名称"
        prop="rightsPackageName"
        align="center"
      />
      <el-table-column
        label="金额"
        prop="rightsAmount"
        align="center"
      />
      <el-table-column
        label="实际金额"
        prop="rightsActAmount"
        align="center"
      />
      <el-table-column
        label="订阅时间"
        prop="subscribeTime"
        align="center"
      />
      <el-table-column
        label="申请退订原因"
        prop="unsubscribeReason"
        align="center"
      />
      <el-table-column
        label="退订申请时间"
        prop="unsubscribeApplyTime"
        align="center"
      />
      <el-table-column
        label="申请人"
        prop="unsubscribeApplyBy"
        align="center"
      />
      <el-table-column
        label="审批状态"
        prop="auditState"
        align="center"
        :formatter="auditStateFormatter"
      />
      <el-table-column
        label="审批时间"
        prop="auditTime"
        align="center"
      />
      <el-table-column
        label="审批人"
        prop="auditBy"
        align="center"
      />
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        width="120px"
      >
        <template slot-scope="scope">
          <div style="display: flex; justify-content: start">
            <el-popconfirm
              v-if="scope.row.auditState === 'AUDITING'"
              title="确认通过这个申请吗？"
              @confirm="handlePass(scope.row)"
            >
              <el-button
                slot="reference"
                type="text"
                size="mini"
              >
                通过
              </el-button>
            </el-popconfirm>
            <el-popconfirm
              v-if="scope.row.auditState === 'AUDITING'"
              title="确认拒绝这个申请吗？"
              @confirm="handleReject(scope.row)"
            >
              <el-button
                slot="reference"
                style="color: #ff0000"
                type="text"
                size="mini"
              >
                拒绝
              </el-button>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="handleSearchList"
    />
  </div>
</template>

<script>
import { get as getDictByName } from "@/api/system/dictDetail"

import {
  queryRightsSubscribeAuditList,
  apAuditPassSubscribe,
  apAuditRejectSubscribe
} from '@/api/rights'

const initState = {
  pageNum: 1,
  pageSize: 10,
  mobile: undefined,
  rightsSupplier: undefined,
  auditState: 'AUDITING',
}

export default {
  name: '',
  data() {
    return {
      queryParams: {
        pageNum: initState.pageNum,
        pageSize: initState.pageSize,
        mobile: initState.mobile,
        rightsSupplier: initState.rightsSupplier,
        auditState: initState.auditState,
      },
      subscribeDateRange: [],
      unsubscribeDateRange: [],
      auditDateRange: [],
      total: 0,

      loading: true,
      dataSource: [],
      rightsSupplierOptions: []
    }
  },
  created() {
    this.handleSearchList()

    getDictByName('rightsSupplier').then(res => {
        this.rightsSupplierOptions = res.content
      })
  },
  methods: {
    // 获取列表
    async handleSearchList() {
      this.loading = true

      const params = {
        ...this.queryParams,
        subscribeStartTime: this.subscribeDateRange.length ? this.subscribeDateRange[0] : undefined,
        subscribeEndTime: this.subscribeDateRange.length ? this.subscribeDateRange[1] : undefined,
        unsubscribeApplyStartTime: this.unsubscribeDateRange.length ? this.unsubscribeDateRange[0] : undefined,
        unsubscribeApplyEndTime: this.unsubscribeDateRange.length ? this.unsubscribeDateRange[1] : undefined,
        auditStartTime: this.auditDateRange.length ? this.auditDateRange[0] : undefined,
        auditEndTime: this.auditDateRange.length ? this.auditDateRange[1] : undefined
      }
      const result = await queryRightsSubscribeAuditList(params).finally(() => {
        this.loading = false
      })

      if (result) {
        this.dataSource = result.data.list
        this.total = result.data.total
      }
    },

    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.handleSearchList()
    },
    handleResetQuery() {
      this.queryParams = {
        ...initState
      }

      this.subscribeDateRange = []
      this.unsubscribeDateRange = []
      this.auditDateRange = []

      this.handleSearchList()
    },
    handlePass(row) {
      apAuditPassSubscribe({
        id: row.id
      }).then(() => {
        this.$message({
          type: 'success',
          message: '通过成功'
        })
        this.handleSearchList()
      })
    },
    handleReject(row) {
      apAuditRejectSubscribe({
        id: row.id
      }).then(() => {
        this.$message({
          type: 'success',
          message: '拒绝成功'
        })
        this.handleSearchList()
      })
    },
    auditStateFormatter(row) {
      if (!row.auditState) return '--';
      return row.auditState === 'AUDITING' ? '待审核' : row.auditState === 'REJECT' ? '审核拒绝' : '审核通过'
    }
    // rightsPayChannelFormat({ rightsSupplier }) {
    //   const obj = this.rightsSupplierOptions.find(item => item.value === rightsSupplier)
    //   return obj ? obj.label : '--'
    // }
  }
}
</script>
