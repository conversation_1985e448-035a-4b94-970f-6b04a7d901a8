<template>
  <div class="app-container">
    <el-form
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item
        label="支付通道名称"
        prop="paymentChannelCode"
        width="150px"
      >
        <el-select
          v-model="queryParams.paymentChannelCode"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="通道状态"
        prop="flowChannel"
        width="150px"
      >
        <el-select
          v-model="queryParams.enabled"
          placeholder="请选择"
          clearable
        >
          <el-option
            label="启用"
            value="Y"
          />
          <el-option
            label="禁用"
            value="N"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          round
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button
          round
          size="mini"
          @click="handleResetQuery"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <div style="margin-bottom: 10px;">
      <el-button
        type="primary"
        round
        size="mini"
        @click="handleAdd"
      >
        新增通道
      </el-button>
    </div>

    <!-- 列表 -->
    <el-table
      v-loading="loading"
      border="border"
      :data="dataSource"
    >
      <el-table-column
        label="支付通道名称"
        prop="paymentChannelName"
        align="center"
      />
      <el-table-column
        label="支付通道编码"
        prop="paymentChannelCode"
        align="center"
      />
      <el-table-column
        label="单笔扣款限额"
        prop="singleAmountUpper"
        align="center"
      />
      <el-table-column
        label="通道状态"
        prop="enabled"
        align="center"
        :formatter="row => row.enabled === 'Y' ? '启用' : '禁用'"
      />
      <el-table-column
        label="修改时间"
        prop="updatedTime"
        align="center"
      />
      <el-table-column
        label="修改人"
        prop="updatedBy"
        align="center"
      />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            @click="handleToggleStatus(scope.row)"
          >
            {{ scope.row.enabled === 'Y' ? '禁用' : '启用' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="handleSearchList"
    /> -->

    <!-- 新增 -->
    <el-dialog
      :title="mode === 'add' ? '新增通道' : '编辑通道'"
      :visible.sync="visible"
      width="500px"
      :before-close="handleClose"
    >
      <el-form
        ref="ruleForm"
        label-position="right"
        label-width="130px"
        :model="ruleForm"
        :rules="rules"
      >
        <el-form-item
          label="支付通道名称"
          prop="paymentChannelName"
        >
          <el-select
            v-model="ruleForm.paymentChannelName"
            placeholder="请选择"
            clearable
            style="width: 100%;"
            :disabled="mode === 'edit'"
            @change="handleChannelNameChange"
          >
            <el-option
              v-for="item in channelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="支付通道编码"
          prop="paymentChannelCode"
        >
          <el-input
            v-model="ruleForm.paymentChannelCode"
            disabled
            placeholder="请填写"
          />
        </el-form-item>
        <el-form-item
          label="单笔扣款限额(元)"
          prop="singleAmountUpper"
        >
          <el-input-number
            v-model="ruleForm.singleAmountUpper"
            style="width: 100%"
            controls-position="right"
            placeholder="请填写"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            round
            @click="handleClose"
          >
            关闭
          </el-button>
          <el-button
            round
            type="primary"
            @click="handleSubmit"
          >
            保存
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { get as getDictByName } from '@/api/system/dictDetail'
import {
  queryPaymentChannelOptions,
  savePaymentChannelConfig,
  queryPaymentChannelConfigList,
  apiPaymentChannelConfigEnable,
  apiPaymentChannelConfigDisable
} from '@/api/setting/deduct'

export default {
  name: '',
  data() {
    const validateName = (rule, value, callback) => {
      if (!value) {
        callback(new Error('不能为空'))
      } else if (this.mode === 'add' && this.dataSource.find(i => i.paymentChannelName === value)) {
        callback(new Error('此支付通道已存在'))
      } else {
        callback()
      }
    }
    return {
      // 查询参数
      queryParams: {
        paymentChannelCode: undefined,
        enabled: undefined,
        pageNum: 1,
        pageSize: 999
      },
      loading: false,
      dataSource: [],
      total: 0,

      channelOptions: [],
      selectedList: [], // 已选择列表
      visible: false,
      mode: 'add',

      ruleForm: {
        paymentChannelName: '',
        paymentChannelCode: undefined,
        singleAmountUpper: undefined,
        enabled: 'Y'
      },
      rules: {
        paymentChannelName: [
          { validator: validateName, trigger: 'blur' }
        ],
        singleAmountUpper: [
          { required: true, message: '不能为空', trigger: 'blur', type: 'number' },
          { min: 0.01, message: '不能小于0.01', trigger: 'blur', type: 'number' },
          { validator: (rule, value, callback) => {
            if (value && value.toString().split('.')[1] && value.toString().split('.')[1].length > 2) {
              callback(new Error('最多保留两位小数'))
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.handleSearchList()

    // this.handleGetSelectedList()
    getDictByName('payChannel').then(res => {
      this.channelOptions = res.content
    })
  },
  methods: {
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.handleSearchList()
    },
    handleResetQuery() {
      this.$refs['queryForm'].resetFields()

      this.queryParams = {
        ...this.queryParams,
        paymentChannelCode: undefined,
        enabled: undefined,
        pageNum: 1
      }

      this.handleSearchList()
    },
    // 获取列表
    handleSearchList() {
      this.loading = true
      queryPaymentChannelConfigList(this.queryParams).then(res => {
        this.dataSource = res.data.list
        this.total = res.data.total
      }).finally(() => {
        this.loading = false
      })
    },
    handleGetSelectedList() {
      queryPaymentChannelOptions().then(res => {
        this.selectedList = res.data
      })
    },
    handleChannelNameChange(value) {
      const target = this.channelOptions.find(o => o.label === value)

      this.ruleForm.paymentChannelCode = target.value
    },

    handleClose() {
      this.visible = false

      this.$refs['ruleForm'].resetFields()
      this.ruleForm = {
        paymentChannelName: undefined,
        paymentChannelCode: undefined,
        enabled: 'Y'
      }
    },

    // 新增
    handleAdd() {
      this.mode = 'add'
      this.visible = true
    },

    // 禁用/启用
    handleToggleStatus({ id, enabled }) {
      const enabledFlag = enabled === 'Y'
      this.$confirm(`是否确认${enabledFlag ? '禁用' : '启用'}支付通道?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        roundButton: true,
        type: 'warning'
      }).then(() => {
        if (enabledFlag) {
          apiPaymentChannelConfigDisable({ id }).then(() => {
            this.$message.success('禁用成功')
            this.handleSearchList()
            // this.handleGetSelectedList()
          })
        } else {
          apiPaymentChannelConfigEnable({ id }).then(() => {
            this.$message.success('启用成功')
            this.handleSearchList()
            // this.handleGetSelectedList()
          })
        }
      })
    },
    // 编辑
    handleEdit(row) {
      this.ruleForm = {
        paymentChannelCode: row.paymentChannelCode,
        paymentChannelName: row.paymentChannelName,
        singleAmountUpper: row.singleAmountUpper,
        enabled: row.enabled
      }
      this.mode = 'edit'
      this.visible = true
    },

    // 保存
    handleSubmit() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          savePaymentChannelConfig({ ...this.ruleForm }).then(() => {
            this.$message.success(this.mode === 'add' ? '创建成功' : '编辑成功')
            this.handleClose()
            this.handleSearchList()
            // this.handleGetSelectedList()
          })
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}
</style>
