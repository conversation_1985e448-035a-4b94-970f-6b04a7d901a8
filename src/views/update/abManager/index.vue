<template>
  <div class="app-container">
    <div style="margin-bottom: 16px;">
      <el-dropdown @command="handleCommand">
        <el-button round type="primary" icon="el-icon-plus" size="mini">新建</el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="android">安卓</el-dropdown-item>
          <el-dropdown-item command="ios">ios</el-dropdown-item>
          <el-dropdown-item command="mini">小程序</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 列表 -->
    <el-table v-loading="loading" border="border" :data="list">
      <el-table-column label="客户端" prop="clientNum" align="center" />
      <el-table-column label="渠道名称" prop="channelName" align="center" />
      <el-table-column label="渠道号" prop="channelNum" align="center" />
      <el-table-column label="A面版本号" prop="versionNum" align="center" />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="hanleEdite(scope.row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 包渠道 -->
    <el-dialog title="包渠道" :visible.sync="visible3" width="500px" :before-close="reset3">
      <el-form ref="ruleForm1" label-position="right" label-width="90px" :model="ruleForm1" :rules="rules1">
        <el-form-item label="渠道名称" prop="channelName">
          <el-input v-model="ruleForm1.channelName" />
        </el-form-item>
        <el-form-item label="渠道号" prop="channelNum">
          <el-input v-model="ruleForm1.channelNum" />
        </el-form-item>
        <el-form-item label="A面版本号" prop="versionNum">
          <el-input v-model="ruleForm1.versionNum" />
        </el-form-item>
        <el-form-item>
          <el-button round type="primary" @click="submitForm1('ruleForm1')">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

  </div>
</template>

<script>
import {
  channelsQuery,
  channelsAdd,
  channelsUpdate
} from '@/api/app'

export default {
  name: '',
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      loading: false,
      list: [],
      total: 0,

      mode: '',

      visible3: false,
      ruleForm1: {
      },
      rules1: {
        channelName: [
          { required: true, message: '请输入渠道名称', trigger: 'blur' }
        ],
        channelNum: [
          { required: true, message: '请输入渠道号', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 新增
    handleCommand(command) {
      this.mode = 'create'
      this.ruleForm1.clientNum = command
      this.visible3 = true
    },

    // 获取列表
    getList() {
      this.loading = true
      channelsQuery(this.queryParams).then(res => {
        this.list = res.data.list
        this.total = res.data.total
        this.loading = false
      })
    },

    // 提交包渠道
    submitForm1(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            ...this.ruleForm1
          }
          if (this.mode === 'edit') {
            channelsUpdate(params).then(res => {
              this.reset3()
              this.getList()
            })
          } else if (this.mode === 'create') {
            channelsAdd(params).then(res => {
              this.reset3()
              this.getList()
            })
          }
        }
      })
    },

    reset3() {
      this.visible3 = false
      this.$refs.ruleForm1.resetFields()
      this.ruleForm1 = {}
    },

    // 编辑
    hanleEdite(row) {
      this.mode = 'edit'
      this.ruleForm1 = { ...row }
      this.visible3 = true
    }

  }
}
</script>

<style scoped>
.list {
  margin-bottom: 16px;
}

.list .item {
  display: flex;
  align-items: center;
}

.list .item span {
  width: 150px;
}
</style>
