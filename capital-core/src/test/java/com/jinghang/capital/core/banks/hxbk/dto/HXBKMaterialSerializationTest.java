package com.jinghang.capital.core.banks.hxbk.dto;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKMaterial;
import com.jinghang.common.util.JsonUtil;
import com.rabbitmq.tools.json.JSONUtil;
import org.junit.Test;

/**
 * 测试HXBKMaterial序列化行为
 */
public class HXBKMaterialSerializationTest {

    @Test
    public void testJacksonSerialization() throws Exception {
        HXBKMaterial material = new HXBKMaterial();
        material.setMType("2");
        material.setBigCode("20");
        material.setSmallCode("201");
        material.setMeterialName("test.jpg");
        material.setFilePath("/path/to/file");

        String json = JSON.toJSONString(material);
        System.out.println("Jackson序列化结果: " + json);
    }

    @Test
    public void testJsonUtilSerialization() throws JsonProcessingException {
        HXBKMaterial material = new HXBKMaterial();
        material.setMType("2");
        material.setBigCode("20");
        material.setSmallCode("201");
        material.setMeterialName("test.jpg");
        material.setFilePath("/path/to/file");

        String json = JSON.toJSONString(material);
        System.out.println("JsonUtil序列化结果: " + json);
    }

    @Test
    public void testArraySerialization() throws JsonProcessingException {
        HXBKMaterial[] materials = new HXBKMaterial[2];

        materials[0] = new HXBKMaterial();
        materials[0].setMType("2");
        materials[0].setBigCode("20");
        materials[0].setSmallCode("201");

        materials[1] = new HXBKMaterial();
        materials[1].setMType("2");
        materials[1].setBigCode("20");
        materials[1].setSmallCode("202");

        String json = JSON.toJSONString(materials);
        System.out.println("JsonUtil数组序列化结果: " + json);
    }
}
