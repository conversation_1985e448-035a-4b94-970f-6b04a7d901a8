package com.jinghang.capital.core.banks.cybk.dto.loan;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKLoanApplyResponse {

    /**
     * 外部放款流水号
     */
    private String outLoanSeq;
    /**
     * 长银授信流水号
     */
    private String applCde;
    /**
     * 长银放款流水号
     */
    private String loanSeq;
    /**
     * 长银借据号
     */
    private String loanNo;
    /**
     * 担保公司码值
     */
    private String guarCompanyCode;
    /**
     * 担保公司名称
     */
    private String guarCompanyName;

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getLoanSeq() {
        return loanSeq;
    }

    public void setLoanSeq(String loanSeq) {
        this.loanSeq = loanSeq;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getGuarCompanyCode() {
        return guarCompanyCode;
    }

    public void setGuarCompanyCode(String guarCompanyCode) {
        this.guarCompanyCode = guarCompanyCode;
    }

    public String getGuarCompanyName() {
        return guarCompanyName;
    }

    public void setGuarCompanyName(String guarCompanyName) {
        this.guarCompanyName = guarCompanyName;
    }
}
