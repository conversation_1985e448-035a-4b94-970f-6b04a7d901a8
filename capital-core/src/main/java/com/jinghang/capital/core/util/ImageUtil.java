package com.jinghang.capital.core.util;

import com.jinghang.common.io.IoUtil;
import net.coobird.thumbnailator.Thumbnails;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 图片工具
 */
public class ImageUtil {

    private static final String DEFAULT_IMG_TYPE = "jpg";
    private static final String DEFAULT_SUFFIX = ".jpg";

    private static final int MAX_FILE_SIZE = 2800000;
    private static final int DEFAULT_IMG_WIDTH = 1080;
    private static final int DEFAULT_IMG_HEIGHT = 1080;
    public static final float DEFAULT_IMG_QUALITY = 0.9f;


    /**
     * 图片缩放(300KB以内)
     * <p></p>
     * 图片缩放，处理分辨率级大小过大问题
     *
     * @param imageInputStream    图片
     */
    public static ByteArrayOutputStream scaleImages(InputStream imageInputStream) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            // outputQuality,图片压缩质量-90%差不多
            Thumbnails.of(imageInputStream)
                .size(DEFAULT_IMG_WIDTH, DEFAULT_IMG_HEIGHT)
                .outputQuality(DEFAULT_IMG_QUALITY)
                .outputFormat(DEFAULT_IMG_TYPE)
                .toOutputStream(os);
        } catch (Exception e) {
            try {
                IoUtil.copy(imageInputStream, os);
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }
        return os;
    }

}
