package com.jinghang.capital.core.vo.credit;



import com.jinghang.capital.core.enums.Education;
import com.jinghang.capital.core.enums.Marriage;

import java.math.BigDecimal;

public class UserInfoVo {

    private String name;
    private String mobile;
    private String livingAddress;

    /**
     * 居住省行政区划代码
     */
    private String livingProvinceCode;

    /**
     * 居住市行政区划代码
     */
    private String livingCityCode;

    /**
     * 居住区行政区划代码
     */
    private String livingDistrictCode;

    private String livingStreet;

    private String email;
    /**
     * 单位名称
     */
    private String unit;
    /**
     * 单位地址
     */
    private String unitAddress;


    /**
     * 单位省行政区划代码
     */
    private String unitProvinceCode;

    /**
     * 单位市行政区划代码
     */
    private String unitCityCode;

    /**
     * 单位区行政区划代码
     */
    private String unitDistrictCode;

    private String unitStreet;

    /**
     * 月薪
     */
    private String income;

    private String industry;
    private String position;
    private String faceChannel;
    private String faceTime;
    private BigDecimal faceScore;


    private String acardScore;

    private String bcardScore;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 经度
     */
    private String longitude;

    /**
     * mac地址
     */
    private String mac;

    /**
     * 婚姻状况
     */
    private Marriage marriage;
    /**
     * 最高学历
     */

    private Education education;

    public String getLivingStreet() {
        return livingStreet;
    }

    public void setLivingStreet(String livingStreet) {
        this.livingStreet = livingStreet;
    }

    public String getUnitStreet() {
        return unitStreet;
    }

    public void setUnitStreet(String unitStreet) {
        this.unitStreet = unitStreet;
    }

    public Marriage getMarriage() {
        return marriage;
    }

    public void setMarriage(Marriage marriage) {
        this.marriage = marriage;
    }

    public Education getEducation() {
        return education;
    }

    public void setEducation(Education education) {
        this.education = education;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getFaceChannel() {
        return faceChannel;
    }

    public void setFaceChannel(String faceChannel) {
        this.faceChannel = faceChannel;
    }

    public String getFaceTime() {
        return faceTime;
    }

    public void setFaceTime(String faceTime) {
        this.faceTime = faceTime;
    }

    public BigDecimal getFaceScore() {
        return faceScore;
    }

    public void setFaceScore(BigDecimal faceScore) {
        this.faceScore = faceScore;
    }

    public String getAcardScore() {
        return acardScore;
    }

    public void setAcardScore(String acardScore) {
        this.acardScore = acardScore;
    }

    public String getBcardScore() {
        return bcardScore;
    }

    public void setBcardScore(String bcardScore) {
        this.bcardScore = bcardScore;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }


    public String getLivingAddress() {
        return livingAddress;
    }

    public void setLivingAddress(String livingAddress) {
        this.livingAddress = livingAddress;
    }

    public String getLivingProvinceCode() {
        return livingProvinceCode;
    }

    public void setLivingProvinceCode(String livingProvinceCode) {
        this.livingProvinceCode = livingProvinceCode;
    }

    public String getLivingCityCode() {
        return livingCityCode;
    }

    public void setLivingCityCode(String livingCityCode) {
        this.livingCityCode = livingCityCode;
    }

    public String getLivingDistrictCode() {
        return livingDistrictCode;
    }

    public void setLivingDistrictCode(String livingDistrictCode) {
        this.livingDistrictCode = livingDistrictCode;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnitAddress() {
        return unitAddress;
    }

    public void setUnitAddress(String unitAddress) {
        this.unitAddress = unitAddress;
    }

    public String getUnitProvinceCode() {
        return unitProvinceCode;
    }

    public void setUnitProvinceCode(String unitProvinceCode) {
        this.unitProvinceCode = unitProvinceCode;
    }

    public String getUnitCityCode() {
        return unitCityCode;
    }

    public void setUnitCityCode(String unitCityCode) {
        this.unitCityCode = unitCityCode;
    }

    public String getUnitDistrictCode() {
        return unitDistrictCode;
    }

    public void setUnitDistrictCode(String unitDistrictCode) {
        this.unitDistrictCode = unitDistrictCode;
    }

    public String getIncome() {
        return income;
    }

    public void setIncome(String income) {
        this.income = income;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }
}
