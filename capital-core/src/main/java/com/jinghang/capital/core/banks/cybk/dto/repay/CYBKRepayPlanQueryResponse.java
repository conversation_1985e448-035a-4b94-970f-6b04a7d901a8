package com.jinghang.capital.core.banks.cybk.dto.repay;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKRepayPlanQueryResponse {
    /**
     * 长银授信流水号
     */
    private String applCde;

    /**
     * 贷款逾期状态
     * M0：无逾期
     * M1：逾期天数1-30天
     * M2：逾期天数31-60天
     * M3：逾期天数61-90
     * M4：逾期天数91-120
     * M5：逾期天数121-150
     * M6：逾期天数151-180
     * M7：逾期天数大于180天
     */
    private String status;

    private List<CYBKRepayPlanInfo> repaymentPlanList = new ArrayList<>();

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<CYBKRepayPlanInfo> getRepaymentPlanList() {
        return repaymentPlanList;
    }

    public void setRepaymentPlanList(List<CYBKRepayPlanInfo> repaymentPlanList) {
        this.repaymentPlanList = repaymentPlanList;
    }
}
