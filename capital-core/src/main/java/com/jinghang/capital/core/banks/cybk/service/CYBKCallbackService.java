package com.jinghang.capital.core.banks.cybk.service;

import com.jinghang.capital.core.banks.AbstractBankCallbackService;
import com.jinghang.capital.core.enums.BankChannel;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> gale
 * @Classname CYBKCallbackService
 * @Description
 * @Date 2025/5/14 00:53
 */
@Service
public class CYBKCallbackService extends AbstractBankCallbackService {
    /**
     * 是否支持该资方渠道
     *
     * @param channel 资方渠道
     * @return 支持情况
     */
    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.CYBK == channel;
    }
}
