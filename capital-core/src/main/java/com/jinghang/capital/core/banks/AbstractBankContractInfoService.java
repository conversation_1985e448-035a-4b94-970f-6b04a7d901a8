package com.jinghang.capital.core.banks;


import com.jinghang.capital.core.dto.SignDynamicParamDto;
import com.jinghang.capital.core.dto.SignTemplateParamDto;
import com.jinghang.capital.core.enums.AgreementType;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.credit.FinCreditService;
import com.jinghang.capital.core.service.loan.FinLoanService;
import com.jinghang.capital.core.service.remote.nfsp.sign.req.SignApplyReq;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * 合同签章相关服务
 */
public abstract class AbstractBankContractInfoService implements BankContractInfoService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private FinCreditService finCreditService;

    @Autowired
    private FinLoanService finLoanService;



    @Override
    public abstract SignApplyReq fetchTemplateSignParam(String businessId, AgreementType agreementType);

    public CommonService getCommonService() {
        return commonService;
    }

    public FinCreditService getFinCreditService() {
        return finCreditService;
    }

    public FinLoanService getFinLoanService() {
        return finLoanService;
    }
}
