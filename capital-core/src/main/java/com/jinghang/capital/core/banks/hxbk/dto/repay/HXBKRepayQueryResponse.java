package com.jinghang.capital.core.banks.hxbk.dto.repay;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKRepayQueryResponse {

    /**
     *还款编号
     */
    @JsonProperty("repay_no")
    private String repayNo;
    /**
     *借据编码
     */
    @JsonProperty("receipt_no")
    private String receiptNo;
    /**
     *客户编号
     */
    @JsonProperty("custom_no")
    private String customNo;
    /**
     *还款类型
     */
    @JsonProperty("repay_type")
    private String repayType;
    /**
     *还款标志:1 线下还款 2 用户主动还款 3 系统代扣
     */
    @JsonProperty("repay_sign")
    private String repaySign;
    /**
     *还款日期
     */
    @JsonProperty("repay_date")
    private String repayDate;
    /**
     *还款账户
     */
    @JsonProperty("repay_account")
    private String repayAccount;
    /**
     *还款状态:Fail:失败 Success:成功 Process:处理中
     */
    @JsonProperty("repay_status")
    private String repayStatus;
    /**
     *失败原因
     */
    @JsonProperty("fail_reason")
    private String failReason;
    /**
     *实还总额
     */
    @JsonProperty("repay_amount")
    private BigDecimal repayAmount;
    /**
     *实还总本金
     */
    @JsonProperty("repay_principal")
    private BigDecimal repayPrincipal;
    /**
     *实还总利息
     */
    @JsonProperty("repay_interest")
    private BigDecimal repayInterest;
    /**
     *实收总罚息
     */
    @JsonProperty("repay_punish")
    private BigDecimal repayPunish;
    /**
     *实收总担保费
     */
    @JsonProperty("guarantee_fee")
    private BigDecimal guaranteeFee;
    /**
     *实收总违约金
     */
    @JsonProperty("liquidated_damages")
    private BigDecimal liquidatedDamages;
    /**
     *实收总服务费
     */
    @JsonProperty("service_charge")
    private BigDecimal serviceCharge;
    /**
     *还款信息列表
     */
    @JsonProperty("repay_infos")
    private List<HXBKRepayInfoDTO> repayInfos;
    /**
     * 结果code
     */
    @JsonProperty("result_code")
    private String resultCode;
    /**
     * 结果信息
     */
    @JsonProperty("result_msg")
    private String resultMsg;
    /**
     * 请求唯一id
     */
    @JsonProperty("req_msg_id")
    private String reqMsgId;

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public String getCustomNo() {
        return customNo;
    }

    public void setCustomNo(String customNo) {
        this.customNo = customNo;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getRepayNo() {
        return repayNo;
    }

    public void setRepayNo(String repayNo) {
        this.repayNo = repayNo;
    }

    public String getRepayAccount() {
        return repayAccount;
    }

    public void setRepayAccount(String repayAccount) {
        this.repayAccount = repayAccount;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }

    public String getRepaySign() {
        return repaySign;
    }

    public void setRepaySign(String repaySign) {
        this.repaySign = repaySign;
    }

    public String getRepayStatus() {
        return repayStatus;
    }

    public void setRepayStatus(String repayStatus) {
        this.repayStatus = repayStatus;
    }

    public BigDecimal getServiceCharge() {
        return serviceCharge;
    }

    public void setServiceCharge(BigDecimal serviceCharge) {
        this.serviceCharge = serviceCharge;
    }

    public BigDecimal getRepayAmount() {
        return repayAmount;
    }

    public void setRepayAmount(BigDecimal repayAmount) {
        this.repayAmount = repayAmount;
    }

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public BigDecimal getRepayInterest() {
        return repayInterest;
    }

    public void setRepayInterest(BigDecimal repayInterest) {
        this.repayInterest = repayInterest;
    }

    public BigDecimal getLiquidatedDamages() {
        return liquidatedDamages;
    }

    public void setLiquidatedDamages(BigDecimal liquidatedDamages) {
        this.liquidatedDamages = liquidatedDamages;
    }

    public BigDecimal getRepayPrincipal() {
        return repayPrincipal;
    }

    public void setRepayPrincipal(BigDecimal repayPrincipal) {
        this.repayPrincipal = repayPrincipal;
    }

    public BigDecimal getRepayPunish() {
        return repayPunish;
    }

    public void setRepayPunish(BigDecimal repayPunish) {
        this.repayPunish = repayPunish;
    }

    public List<HXBKRepayInfoDTO> getRepayInfos() {
        return repayInfos;
    }

    public void setRepayInfos(List<HXBKRepayInfoDTO> repayInfos) {
        this.repayInfos = repayInfos;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getReqMsgId() {
        return reqMsgId;
    }

    public void setReqMsgId(String reqMsgId) {
        this.reqMsgId = reqMsgId;
    }

    /**
     * 成功
     * @return 结果
     */
    public boolean isSuccess() {//成功
        return Objects.equals(repayStatus, "Success");
    }

    /**
     * 失败
     * @return 结果
     */
    public boolean isFail() {//失败
        return Objects.equals(repayStatus, "Fail");
    }

    /**
     * 处理中
     * @return 结果
     */
    public boolean isProcess() {//处理中
        return Objects.equals(repayStatus, "Process");
    }

}
