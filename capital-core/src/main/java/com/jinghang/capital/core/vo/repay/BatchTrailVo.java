package com.jinghang.capital.core.vo.repay;




import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.RepayPurpose;
import com.jinghang.capital.core.enums.RepayType;

import java.math.BigDecimal;
import java.util.List;

public class BatchTrailVo {

    /**
     * 资方渠道
     */
    private BankChannel channel;

    /**
     * 还款模式
     */
    private RepayPurpose repayPurpose;
    /**
     * 还款类型
     */
    private RepayType repayType;

    /**
     * 减免总额
     */
    private BigDecimal totalReduceAmt;

    /**
     * 还款试算列表
     */
    private List<BatchTrailItemVo> trailItems;

    /**
     * 还款数据列表
     */
    private List<RepayPlanItemVo> applyList;

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public RepayType getRepayType() {
        return repayType;
    }

    public void setRepayType(RepayType repayType) {
        this.repayType = repayType;
    }

    public List<BatchTrailItemVo> getTrailItems() {
        return trailItems;
    }

    public void setTrailItems(List<BatchTrailItemVo> trailItems) {
        this.trailItems = trailItems;
    }

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public BigDecimal getTotalReduceAmt() {
        return totalReduceAmt;
    }

    public void setTotalReduceAmt(BigDecimal totalReduceAmt) {
        this.totalReduceAmt = totalReduceAmt;
    }

    public List<RepayPlanItemVo> getApplyList() {
        return applyList;
    }

    public void setApplyList(List<RepayPlanItemVo> applyList) {
        this.applyList = applyList;
    }
}
