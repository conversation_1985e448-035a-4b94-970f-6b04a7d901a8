package com.jinghang.capital.core.util;


import com.jinghang.common.util.StringUtil;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * 计算金额
 */
public final class AmountUtil {
    private AmountUtil() {
    }

    public static <T> BigDecimal calcSumAmount(List<T> plans, Function<T, BigDecimal> func) {
        return plans.stream().map(func)
            .map(i -> Objects.requireNonNullElse(i, BigDecimal.ZERO))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal subtract(BigDecimal... amount) {
        return Arrays.stream(amount)
            .map(i -> Objects.requireNonNullElse(i, BigDecimal.ZERO))
            .reduce(BigDecimal::subtract)
            .orElse(BigDecimal.ZERO);
    }
    public static BigDecimal sum(BigDecimal... amount) {
        return Arrays.stream(amount)
            .map(i -> Objects.requireNonNullElse(i, BigDecimal.ZERO))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal toBigDecimal(String str) {
        return StringUtil.isEmpty(str) ? BigDecimal.ZERO : new BigDecimal(str);
    }

    public static BigDecimal safeNum(BigDecimal amount) {
        return Objects.requireNonNullElse(amount, BigDecimal.ZERO);
    }
}
