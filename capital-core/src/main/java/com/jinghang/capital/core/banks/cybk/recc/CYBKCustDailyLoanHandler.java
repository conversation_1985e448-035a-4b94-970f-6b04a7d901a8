package com.jinghang.capital.core.banks.cybk.recc;

import com.jinghang.capital.core.banks.cybk.recc.dto.CYBKCustDailyLoan;
import com.jinghang.capital.core.banks.cybk.recc.dto.CYBKLoanReplanDTO;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.vo.recc.ReccType;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/28 15:37
 * <p>
 * 长银直连 对客日终借据文件
 * <p>
 */
@Component
public class CYBKCustDailyLoanHandler extends CYBKReccAbstractHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKCustDailyLoanHandler.class);
    private static final int TWO = 2;

    @Autowired
    private WarningService warningService;

    @Override
    public void process(LocalDate reccDay) {

        //获取日终发生变更的数据(还款计划维度)
        List<CYBKLoanReplanDTO> allList = findCYBKLoanPlanDailyHasChanged(reccDay);

        if (CollectionUtils.isEmpty(allList)) {
            logger.info("日期" + reccDay.toString() + " 没有待处理订单");
        }

        //转换为借据维度,方便写文件时填充
        Map<String, CYBKCustDailyLoan> loanMap = getDailyLoanMap(allList, reccDay);

        File sourceFile = null;
        File tempDir = null;
        File okFile = null;
        try {
            Path path = Files.createTempDirectory("CYBK");
            tempDir = path.toFile();
            String filePre = "CYBK" + DateFormatUtils.format(new Date(), "yyyyMMdd") + "对客放款明细文件";
            sourceFile = File.createTempFile(tempDir.getAbsolutePath() + filePre, ".csv");
            CSVPrinter printer = CSVFormat.DEFAULT.withSkipHeaderRecord()
                .withDelimiter(',').print(sourceFile, StandardCharsets.UTF_8);

            //表头
            // 合作机构贷款唯一编号,长银授信流水号,放款申请流水号,会计日,借据状态,结清日期,五级分类标识,下一还款日期,未结清期数",逾期期次数,本金逾期天数,利息逾期天数,本金余额（单位分）,
            // 利息余额（单位分）,应收罚息金额,逾期罚息余额（单位分）,应计非应计标识（对客）",核销标识（对客）,扩展信息
            List<String> header = List.of(
                "loan_seq", "appl_seq", "out_appl_seq", "settle_date", "status", "clear_date", "asset_class", "next_repay_date", "unclear_terms",
                "ovd_terms", "prcp_ovd_days", "int_ovd_days", "prcp_bal", "int_bal", "od_prcp", "od_prcp_bal", "accrued_status", "write_off", "ext_info"
            );
            printer.printRecord(header);

            for (String loanId : loanMap.keySet()) {
                CYBKCustDailyLoan dailyLoan = loanMap.get(loanId);

                List<String> custLoanDTOList = new ArrayList<>();
                custLoanDTOList.add(dailyLoan.getLoanSeq());
                custLoanDTOList.add(dailyLoan.getApplSeq());
                custLoanDTOList.add(dailyLoan.getOutApplSeq()); //授信申请流水号
                custLoanDTOList.add(dailyLoan.getSettleDate());
                custLoanDTOList.add(dailyLoan.getStatus());
                custLoanDTOList.add(dailyLoan.getClearDate());
                custLoanDTOList.add(dailyLoan.getAssetClass());
                custLoanDTOList.add(dailyLoan.getNextRepayDate());
                custLoanDTOList.add(dailyLoan.getUnpaidPeriods().toString());
                custLoanDTOList.add(dailyLoan.getOverduePeriods().toString());
                custLoanDTOList.add(dailyLoan.getOverdueDays());
                custLoanDTOList.add(dailyLoan.getOverdueDays());
                custLoanDTOList.add(dailyLoan.getPrincipal().movePointRight(TWO).toPlainString());
                custLoanDTOList.add(dailyLoan.getInterest().movePointRight(TWO).toPlainString());
                custLoanDTOList.add(dailyLoan.getPenalty().movePointRight(TWO).toPlainString());
                custLoanDTOList.add(dailyLoan.getPenalty().movePointRight(TWO).toPlainString());
                custLoanDTOList.add("0");
                custLoanDTOList.add("N");
                custLoanDTOList.add("");
                printer.printRecord(custLoanDTOList);
            }

            printer.close();

            String uploadPath = getCustReccFilePath(reccDay);

            String dateStr = reccDay.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            // /download/cyxf/{产品编码}/out/files/{YYYYMMDD}/eod_loan_daily_${yyyymmdd}.csv
            String fileName = "eod_loan_daily_" + dateStr + ".csv";
            String okFileName = "eod_loan_daily_" + dateStr + ".csv.ok";
            // 上传oss
            logger.info("长银直连上传对客日终借据文件");
            getCybkSftpService().upload("/" + uploadPath + fileName, sourceFile.getAbsolutePath());

            // 生成MD5  ok 文件
            byte[] fileContent = Files.readAllBytes(sourceFile.toPath());
            Path localVerifyFilePath = Files.createTempFile("loan_" + dateStr, ".csv.ok");
            OutputStream verifyOs = Files.newOutputStream(localVerifyFilePath);
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(String.valueOf(loanMap.keySet().size()).getBytes(StandardCharsets.UTF_8));
            IOUtils.copy(byteArrayInputStream, verifyOs);
            getCybkSftpService().upload("/" + uploadPath + okFileName, localVerifyFilePath.toAbsolutePath().toString());
            okFile = localVerifyFilePath.toFile();
        } catch (Exception e) {
            logger.error("长银直连上传对客日终借据文件异常", e);
            warningService.warn("长银直连上传对客日终借据文件异常");
        } finally {
            if (sourceFile != null) {
                sourceFile.delete();
            }
            if (okFile != null) {
                okFile.delete();
            }
            if (tempDir != null) {
                tempDir.delete();
            }
        }
    }

    private List<CYBKLoanReplanDTO> getDailyData(LocalDate reccDay) {
        //前一天放款成功的还款计划
        //List<CYBKLoanReplanDTO> loanReplanDTOList = findCustReccLoanReplans(reccDay);
        ////前一天到期结息的还款计划
        //List<CYBKLoanReplanDTO> dueDateList = findCYBKDailyLoanReplanRepayDateByDueDate(reccDay);
        ////转逾期(只查逾期1天、31天的）
        //List<CYBKLoanReplanDTO> custOverduePlan = findCustOverduePlan(reccDay);
        ////发生还款
        //List<CYBKLoanReplanDTO> custRepayList = findCustOverdueRepay(reccDay);

        // 合并 并去重复
        List<CYBKLoanReplanDTO> allList = findCYBKLoanPlanDailyHasChanged(reccDay);
        return allList;
    }

    @Override
    public ReccType getReccType() {
        return ReccType.CUST_DAILY_LOAN;
    }
}
