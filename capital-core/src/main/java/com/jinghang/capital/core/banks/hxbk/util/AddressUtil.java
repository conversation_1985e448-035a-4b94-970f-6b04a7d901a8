package com.jinghang.capital.core.banks.hxbk.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 身份证地址提取省市区工具类
 *
 * @Author: Lior
 * @CreateTime: 2025-07-09
 */
public class AddressUtil {

    /**
     * 地址信息对象
     */
    public static class AddressInfo {
        private String province;
        private String city;
        private String area;
        private String detail;

        public AddressInfo() {
        }

        public AddressInfo(String province, String city, String area, String detail) {
            this.province = province;
            this.city = city;
            this.area = area;
            this.detail = detail;
        }

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getArea() {
            return area;
        }

        public void setArea(String area) {
            this.area = area;
        }

        public String getDetail() {
            return detail;
        }

        public void setDetail(String detail) {
            this.detail = detail;
        }

        @Override
        public String toString() {
            return "AddressInfo{" +
                    "province='" + province + '\'' +
                    ", city='" + city + '\'' +
                    ", area='" + area + '\'' +
                    ", detail='" + detail + '\'' +
                    '}';
        }
    }

    /**
     * 根据身份证地址提取省市区工具类
     *
     * @param address
     * @return
     */
    public static AddressInfo addressResolution(String address) {
        // 处理null或空字符串
        if (address == null || address.trim().isEmpty()) {
            return new AddressInfo("未知省份", "未知城市", "未知区域", "");
        }

        // 优化正则表达式，增加对镇、乡、街道、办事处等行政单位的支持
        String regex = "(?<province>[^省市区县镇乡]+?(省|自治区|特别行政区|市)|上海市|北京市|天津市|重庆市)(?<city>市辖区|([^省市区县镇乡]+?(市|自治州|地区|盟|区划|行政单位)))?(?<area>([市]?辖区|开发区|[^省市县镇乡]+?(县|区|市|旗|岛|自治县|自治旗|林区|特区|镇|乡|街道|办事处|苏木|民族乡|民族苏木)))?(?<detail>.*)";
        Matcher m = Pattern.compile(regex).matcher(address.trim());
        String province = null, city = null, area = null, detail = null;

        if (m.find()) {
            province = m.group("province");
            city = m.group("city");
            area = m.group("area");
            detail = m.group("detail");

            // 处理省份：确保不为空
            String finalProvince = (province == null || province.trim().isEmpty()) ? "未知省份" : province.trim();

            // 处理城市：如果为空则使用省份（直辖市处理）
            String finalCity = (city == null || city.trim().isEmpty()) ? finalProvince : city.trim();

            // 处理区域：确保不为空
            String finalArea = (area == null || area.trim().isEmpty()) ? "未知区域" : area.trim();

            // 处理详细地址：确保不为空
            String finalDetail = (detail == null || detail.trim().isEmpty()) ? "" : detail.trim();

            return new AddressInfo(finalProvince, finalCity, finalArea, finalDetail);
        }

        // 如果解析失败，返回默认值确保省市区不为空
        return new AddressInfo("未知省份", "未知城市", "未知区域", "");
    }

    /**
     * 测试用户提到的问题地址
     */
    public static void testProblemAddresses() {
        System.out.println("=== 用户问题地址测试 ===");
        System.out.println("测试地址1: 广东省东莞市长安镇振安东路");
        System.out.println(addressResolution("广东省东莞市长安镇振安东路"));
        System.out.println("测试地址2: 安徽省诸头市龙潭乡妫栋7号80号院");
        System.out.println(addressResolution("安徽省诸头市龙潭乡妫栋7号80号院"));
        System.out.println();
    }

    public static void main(String[] args) {
        testProblemAddresses();
        System.out.println("=== 正常省市区+街道情况 ===");
        System.out.println(addressResolution("广东省深圳市福田区梅林街道办事处国际金融科技大厦"));
        System.out.println(addressResolution("山东省德州市禹城市伦镇堂子街村235号"));
        System.out.println(addressResolution("江苏省南京市玄武区新街口街道中山路18号"));
        System.out.println(addressResolution("浙江省杭州市西湖区文三街道文三路259号"));

        System.out.println("\n=== 直辖市情况 ===");
        System.out.println(addressResolution("北京市朝阳区三里屯街道工体北路"));
        System.out.println(addressResolution("上海市浦东新区陆家嘴街道世纪大道88号"));
        System.out.println(addressResolution("天津市和平区南营门街道南京路108号"));
        System.out.println(addressResolution("重庆市渝中区解放碑街道民权路28号"));

        System.out.println("\n=== 自治区情况 ===");
        System.out.println(addressResolution("新疆维吾尔自治区乌鲁木齐市天山区"));
        System.out.println(addressResolution("西藏自治区拉萨市城关区八廓街道"));
        System.out.println(addressResolution("内蒙古自治区呼和浩特市新城区"));

        System.out.println("\n=== 特别行政区情况 ===");
        System.out.println(addressResolution("香港特别行政区中西区中环"));
        System.out.println(addressResolution("澳门特别行政区花地玛堂区"));

        System.out.println("\n=== 县级市情况 ===");
        System.out.println(addressResolution("江苏省苏州市昆山市玉山镇人民路100号"));
        System.out.println(addressResolution("广东省东莞市长安镇振安东路"));
        System.out.println(addressResolution("安徽省诸头市龙潭乡妫栋7号80号院"));

        System.out.println("\n=== 镇乡街道办事处情况 ===");
        System.out.println(addressResolution("河北省石家庄市正定县正定镇常山西路"));
        System.out.println(addressResolution("山西省太原市小店区坞城街道学府街"));
        System.out.println(addressResolution("湖南省长沙市岳麓区桔子洲街道潇湘中路"));
        System.out.println(addressResolution("四川省成都市武侯区跳伞塔街道人民南路"));
        System.out.println(addressResolution("内蒙古自治区呼和浩特市土默特左旗毕克齐镇"));
        System.out.println(addressResolution("新疆维吾尔自治区乌鲁木齐市达坂城区达坂城镇"));

        System.out.println("\n=== 缺少部分信息情况 ===");
        System.out.println(addressResolution("广东省深圳市"));
        System.out.println(addressResolution("北京市朝阳区"));
        System.out.println(addressResolution("上海市"));

        System.out.println("\n=== 异常情况 ===");
        System.out.println(addressResolution(""));
        System.out.println(addressResolution(null));
        System.out.println(addressResolution("无效地址123"));
        System.out.println(addressResolution("美国纽约州纽约市"));
    }
}