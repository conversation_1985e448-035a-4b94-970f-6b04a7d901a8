package com.jinghang.capital.core.banks.cybk.recc;



import com.jinghang.capital.core.banks.cybk.enums.CYBKReccFileType;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.ReccStateEnum;
import com.jinghang.capital.core.repository.LoanReplanRepository;
import com.jinghang.capital.core.vo.recc.ReccType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class CYBKReccPlanHandler extends CYBKReccAbstractHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKReccAbstractHandler.class);

    @Override
    public void process(LocalDate reccDay) {
        //长银还款计划文件
        CYBKReconcileFile reconcileFile = findReconcileFile(reccDay, CYBKReccFileType.PLAN_FILE);
        reconcileFile.setReccDate(LocalDate.now());

        String reccId = reconcileFile.getId();

        // 查到长银的放款成功的还款计划表数据信息
        List<CYBKReccPlan> reccPlans = findReccPlanFileRecords(reccId);

        // 查询本系统的还款计划表数据信息
        List<LoanReplan> loanReplans = findLoanReplanRecords(BankChannel.CYBK.getCode(),reccDay);

        if (reccPlans.size() == 0 && loanReplans.size() == 0) {
            reconcileFile.setReccState(ReccStateEnum.S.name());
            updateCYBKReconcileFile(reconcileFile);
            return;
        }
        if (reccPlans.size() != loanReplans.size()) {
            logger.warn("长银直连对账 还款计划 成功条数不一致 reccType：{} reccDay：{} 业务方条数：{} 资方条数：{}", CYBKReccFileType.REPAYMENT_FILE, reccDay, loanReplans.size(),
                    reccPlans.size());
            getWarningService().warn("\n长银直连对账:" + CYBKReccFileType.REPAYMENT_FILE + "\n对账日:" + reccDay + "\n 放款后还款计划条数不一致 ");
        }

        List<CYBKReccPlan> successList = new ArrayList<>();

        reccPlans.forEach(rp -> {
            // 拿到loanId
            var loanId = rp.getSysId();
            var existRecord = filterPlanInter(loanReplans, loanId, rp.getPeriod());
            boolean match = match(rp, existRecord);
            rp.setReccStatus(match ? ReccStateEnum.S.name() : ReccStateEnum.F.name());
            if (match) {
                successList.add(rp);
            } else {
                //对账失败打印日志
                warningLog(rp, existRecord, loanId);
            }
            updateReccReplan(rp);
        });
        boolean allMatch = successList.size() == reccPlans.size() && successList.size() == loanReplans.size();
        reconcileFile.setReccState(allMatch ? ReccStateEnum.S.name() : ReccStateEnum.F.name());
        updateCYBKReconcileFile(reconcileFile);
        //对账失败，企业微信告警
        if (ReccStateEnum.F.name().equals(reconcileFile.getReccState())) {
            getWarningService().warn("\n长银直连 放款成功后还款计划 对账失败:" + CYBKReccFileType.PLAN_FILE + "\n对账日:" + reccDay + "\n，对账成功总数与系统还款计划总数不一致 ");
        }
    }



    private List<LoanReplan> findLoanReplanRecords(String channel, LocalDate reccDay) {
        LocalDateTime startOfDay = reccDay.atStartOfDay();
        LocalDateTime nextDayStart = reccDay.plusDays(1L).atStartOfDay();
        return getLoanReplanRepository().findByChannelAndCreateDate(channel, startOfDay, nextDayStart);
    }


    private LoanReplan filterPlanInter(List<LoanReplan> recordList, String loanId, Integer period) {
        return recordList.stream().filter(record -> record.getLoanId().equals(loanId) && period.equals(record.getPeriod())).findAny().orElse(null);
    }

    private boolean match(CYBKReccPlan reccRepay, LoanReplan repayRecord) {
        if (repayRecord == null) {
            return false;
        }

        return reccRepay.getRepayPrincipalAmt().compareTo(repayRecord.getPrincipalAmt()) == 0
                && reccRepay.getRepayInterestAmt().compareTo(repayRecord.getInterestAmt()) == 0
                && reccRepay.getRepayPenaltyAmt().compareTo(repayRecord.getPenaltyAmt()) == 0
                && reccRepay.getRepayGuaranteeAmt().compareTo(repayRecord.getGuaranteeAmt()) == 0;
    }


    private void warningLog(CYBKReccPlan lf, LoanReplan existRecord, String loanId) {
        Integer sysPeriod = null;
        if (Objects.nonNull(existRecord)) {
            sysPeriod = existRecord.getPeriod();
        }
        logger.warn("长银直连 放款成功后还款计划 对账失败，reccType：[{}] 资方loanId：[{}]，还款第[{}]期",
            CYBKReccFileType.PLAN_FILE, loanId, sysPeriod);
    }

    @Override
    public ReccType getReccType() {
        return ReccType.PLAN;
    }
}
