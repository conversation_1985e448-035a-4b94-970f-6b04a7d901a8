package com.jinghang.capital.core.banks.hxbk.dto.loan;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.jinghang.capital.core.banks.hxbk.config.GenericJsonSerializer;
import com.jinghang.capital.core.banks.hxbk.dto.HXBKBaseRequest;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKMaterial;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;

import java.math.BigDecimal;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKLoanApplyRequest extends HXBKBaseRequest {

    @JsonIgnore
    private static final HXBKMethod METHOD = HXBKMethod.LOAN_APPLY;

    /**
     * 订单号 capital.loan.id
     */
    @JsonProperty("order_no")
    private String orderNo;

    /**
     * 蚂蚁授信流水号 capital.credit.creditNo
     */
    @JsonProperty("original_order_no")
    private String originalOrderNo;

    /**
     * 资产方用户唯一标识 credit.accountId
     */
    @JsonProperty("open_id")
    private String openId;

    /**
     * 渠道类型 XHBKChannelType  不是必传
     */
    @JsonProperty("channel_type")
    private String channelType;
    /**
     * 客户类型  不是必传
     */
    @JsonProperty("custom_type")
    private String customType;


    /**
     * 资金方编码 取固定值 D20250701000000001
     */
    @JsonProperty("fund_code")
    private String fundCode;

    /**
     * 客户号  必传 credit.customNo
     */
    @JsonProperty("custom_no")
    private String customNo;

    /**
     * 借款金额 及 授信年利率
     */
    @JsonProperty("risk_data")
    @JsonSerialize(using = GenericJsonSerializer.class)
    private RiskData riskData;

    /**
     * 银行卡号
     */
    @JsonProperty("bank_card_no")
    private String bankCardNo;
    /**
     * 放款金额
     */
    @JsonProperty("loan_amount")
    private BigDecimal loanAmount;
    /**
     * 期数，申请期限
     */
    private BigDecimal period;
    /**
     * 还款方式 1等额本息 2 等额本金
     */
    @JsonProperty("repay_type")
    private String repayType;

    /**
     * 贷款用途
     */
    @JsonProperty("loan_way")
    private String loanWay;

    /**
     * Material
     */
    private HXBKMaterial[] materials;

    /**
     * 还款日
     */
    @JsonProperty("repay_date")
    private String repayDate;


    /**
     * 预留手机号
     */
    @JsonProperty("reserved_mobile")
    private String reservedMobile;

    /**
     * 账号开户名
     */
    @JsonProperty("bank_card_name")
    private String bankCardName;

    /**
     * 账号开户行
     */
    @JsonProperty("bank_card_adress")
    private String bankCardAdress;

    /**
     * 优惠券id
     */
    @JsonProperty("coupon_id")
    private String couponId;


    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getReservedMobile() {
        return reservedMobile;
    }

    public void setReservedMobile(String reservedMobile) {
        this.reservedMobile = reservedMobile;
    }

    public String getBankCardName() {
        return bankCardName;
    }

    public void setBankCardName(String bankCardName) {
        this.bankCardName = bankCardName;
    }

    public String getBankCardAdress() {
        return bankCardAdress;
    }

    public void setBankCardAdress(String bankCardAdress) {
        this.bankCardAdress = bankCardAdress;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }

    public HXBKMaterial[] getMaterials() {
        return materials;
    }

    public void setMaterials(HXBKMaterial[] materials) {
        this.materials = materials;
    }

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getCustomNo() {
        return customNo;
    }

    public void setCustomNo(String customNo) {
        this.customNo = customNo;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public BigDecimal getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(BigDecimal loanAmount) {
        this.loanAmount = loanAmount;
    }

    public BigDecimal getPeriod() {
        return period;
    }

    public void setPeriod(BigDecimal period) {
        this.period = period;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public String getLoanWay() {
        return loanWay;
    }

    public void setLoanWay(String loanWay) {
        this.loanWay = loanWay;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCustomType() {
        return customType;
    }

    public void setCustomType(String customType) {
        this.customType = customType;
    }

    public RiskData getRiskData() {
        return riskData;
    }

    public void setRiskData(RiskData riskData) {
        this.riskData = riskData;
    }

    public String toJson() throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.writeValueAsString(this);
    }

    public static class RiskData{
        /**
         * 用信金额，单位为分。1500000表示提现金额为15000元
         */
        @JsonProperty("loan_amount")
        private BigDecimal loanAmount;

        /**
         * 授信年利率，精确到小数点后4位0.2388，表示年利率为23.88%
         */
        @JsonProperty("credit_rate")
        private BigDecimal creditRate;

        public BigDecimal getLoanAmount() {
            return loanAmount;
        }

        public void setLoanAmount(BigDecimal loanAmount) {
            this.loanAmount = loanAmount;
        }

        public BigDecimal getCreditRate() {
            return creditRate;
        }

        public void setCreditRate(BigDecimal creditRate) {
            this.creditRate = creditRate;
        }
    }



    @Override
    public HXBKMethod getMethod() {
        return METHOD;
    }
}
