package com.jinghang.capital.core.banks.cybk.service;

import com.jinghang.capital.api.dto.FundingModel;
import com.jinghang.capital.core.banks.AbstractBankQuotaService;
import com.jinghang.capital.core.banks.cybk.config.CYBKConfig;
import com.jinghang.capital.core.banks.cybk.convert.CYBKCreditConvert;
import com.jinghang.capital.core.banks.cybk.dto.limit.*;
import com.jinghang.capital.core.banks.cybk.remote.CYBKRequestService;
import com.jinghang.capital.core.dto.QuotaAdjustApplyDto;
import com.jinghang.capital.core.dto.QuotaQueryDto;
import com.jinghang.capital.core.dto.QuotaQueryResultDto;
import com.jinghang.capital.core.entity.CYBKCreditFlow;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.QuotaAdjustRecord;
import com.jinghang.capital.core.entity.QuotaCycleUserInfo;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.CreditStatus;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.repository.CYBKCreditFlowRepository;
import com.jinghang.capital.core.repository.QuotaAdjustRecordRepository;
import com.jinghang.capital.core.service.credit.FinCreditService;
import com.jinghang.capital.core.service.event.QuotaQueryResultEvent;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> gale
 * @Classname CYBKQuotaService
 * @Description TODO
 * @Date 2025/5/14 00:49
 */
@Service
public class CYBKQuotaService extends AbstractBankQuotaService {
    private static final Logger logger = LoggerFactory.getLogger(CYBKQuotaService.class);
    @Autowired
    private CYBKConfig config;
    @Autowired
    private CYBKRequestService cybkRequestService;
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    @Autowired
    private QuotaAdjustRecordRepository quotaAdjustRecordRepository;
    @Autowired
    private FinCreditService finCreditService;

    @Autowired
    private CYBKCreditFlowRepository cybKCreditFlowRepository;


    /**
     * 请求资方额度查询
     *
     * @param quotaCycleUserInfo
     */
    @Override
    protected QuotaQueryResultDto queryBankQuota(QuotaCycleUserInfo quotaCycleUserInfo) {
        CYBKLimitQueryRequest cybkLimitQueryRequest = new CYBKLimitQueryRequest();
        cybkLimitQueryRequest.setOutApplSeq(quotaCycleUserInfo.getCreditSeq());
        cybkLimitQueryRequest.setApplCde(quotaCycleUserInfo.getBankCreditSeq());
        cybkLimitQueryRequest.setTerminalType("13");
        cybkLimitQueryRequest.setMerchantNo(config.getMerchantCode());
        cybkLimitQueryRequest.setStoreCode(config.getMerchantShop());
        logger.info("长银直连额度查询请求:{}", JsonUtil.toJsonString(cybkLimitQueryRequest));
        CYBKLimitQueryResponse response = cybkRequestService.limitQuery(cybkLimitQueryRequest);
        if (Objects.isNull(response)) {
            return null;
        }else {
            logger.info("长银直连额度查询响应:{}", JsonUtil.toJsonString(response));
            return CYBKCreditConvert.INSTANCE.toVo(response);
        }
    }
    @Override
    public void quotaQuery(QuotaQueryDto dto) {
        QuotaCycleUserInfo userInfo = getQuotaCycleUserInfo(dto.getStage(), dto.getBusinessId());
        QuotaQueryResultDto resultDto = queryBankQuota(userInfo);
        //查询失败重查
        if (resultDto == null) {
            // 丢延迟队列，异步查询授信结果
            getMqService().submitQuotaQueryDelay(JsonUtil.toJsonString(dto));
            return;
        }
        //查询额度成功，处理后续逻辑
        eventPublisher.publishEvent(new QuotaQueryResultEvent(dto, resultDto));
    }
    /**
     * 调额申请
     *
     * @param adjustId
     */
    @Override
    public void quotaAdjustApply(QuotaAdjustApplyDto applyDto,QuotaAdjustRecord record) {
          CYBKLimitAdjustApplyRequest request = new CYBKLimitAdjustApplyRequest();
          request.setAdjustNo(applyDto.getAdjustNo());
          request.setOutAdjustNo(applyDto.getOutAdjustNo());
          request.setMerchantNo(config.getMerchantCode());
          request.setStoreCode(config.getMerchantShop());
          request.setLoanType(config.getLoanType());
          request.setApplCde(applyDto.getApplCde());
          request.setAdjustType(applyDto.getAdjustType());
          request.setNewLimitStatus(applyDto.getNewLimitStatus());
          request.setNewLimitAmt(applyDto.getNewLimitAmt());
          request.setEndDate(applyDto.getEndDate());
        logger.info("长银直连调额请求:{}", JsonUtil.toJsonString(request));
        CYBKLimitAdjustApplyResponse applyResp = cybkRequestService.applyAdjustQuota(request);
        logger.info("长银直连调额请求响应:{}", JsonUtil.toJsonString(applyResp));
        if (Objects.isNull(applyResp)) {
            getMqService().submitQuotaAdjustApplyDelay(JsonUtil.toJsonString(applyDto));
        }else {
            record.setCreditSeq(record.getId());
            record.setBankQuotaAdjustSeq(applyResp.getAdjustNo());
            Credit credit = finCreditService.getCredit(record.getBusinessId());
             switch (applyResp.getAdjustStatus()) {
                 case "99","02" ->{
                     record.setStatus(ProcessStatus.FAIL);
                     credit.setCreditStatus(CreditStatus.FAIL);
                     record.setRemark(applyResp.getAdjustStatusDesc());
                     credit.setRemark(applyResp.getAdjustStatusDesc());
                 }
                 case "01" ->{
                     record.setStatus(ProcessStatus.SUCCESS);
                     credit.setPassTime(LocalDateTime.now());
                     credit.setCreditResultAmt(applyDto.getNewLimitAmt());
                     credit.setCreditStatus(CreditStatus.SUCCESS);
                     saveCreditFlow(credit.getId(),record.getBankCreditSeq(),record.getBankUserId());
                 }
                 default ->  {
                     record.setStatus(ProcessStatus.PROCESSING);
                     credit.setCreditStatus(CreditStatus.PROCESSING);
                     //发送mq消息，查询调额结果
                     getMqService().submitQuotaAdjustResultQueryDelay(record.getId());
                 }
             }
             finCreditService.updateCredit(credit);
             quotaAdjustRecordRepository.save(record);
        }

    }

    /**
     * 调额申请结果查询
     *
     * @param adjustId
     */
    @Override
    public void quotaAdjustResultQuery(String adjustId) {
        QuotaAdjustRecord record = quotaAdjustRecordRepository.findById(adjustId).orElse(null);
        if (record != null) {
            CYBKLimitApplyQueryRequest request = new CYBKLimitApplyQueryRequest();
            request.setOutAdjustNo(adjustId);
            request.setAdjustNo(record.getBankQuotaAdjustSeq());
            logger.info("长银直连调额结果查询请求:{}", JsonUtil.toJsonString(request));
            CYBKLimitApplyQueryResponse queryResponse = cybkRequestService.quotaAdjustQuery(request);
            logger.info("长银直连调额结果查询响应:{}", JsonUtil.toJsonString(queryResponse));

            if (Objects.isNull(queryResponse)) {
                getMqService().submitQuotaAdjustResultQueryDelay(record.getId());
            }else {
                record.setCreditSeq(record.getId());
                Credit credit = finCreditService.getCredit(record.getBusinessId());
                switch (queryResponse.getAdjustStatus()) {
                    case "99","02" ->{
                        record.setStatus(ProcessStatus.FAIL);
                        credit.setCreditStatus(CreditStatus.FAIL);
                        record.setRemark(queryResponse.getAdjustStatusDesc());
                        credit.setRemark(queryResponse.getAdjustStatusDesc());
                    }
                    case "01" ->{
                        record.setStatus(ProcessStatus.SUCCESS);
                        credit.setCreditStatus(CreditStatus.SUCCESS);
                        credit.setPassTime(LocalDateTime.now());
                        credit.setCreditResultAmt(record.getNeedQuotaAmt());
                        saveCreditFlow(credit.getId(),record.getBankCreditSeq(),record.getBankUserId());

                    }
                    default ->  {
                        record.setStatus(ProcessStatus.PROCESSING);
                        credit.setCreditStatus(CreditStatus.PROCESSING);
                        //发送mq消息，查询调额结果
                        getMqService().submitQuotaAdjustResultQueryDelay(record.getId());
                    }
                }
                finCreditService.updateCredit(credit);
                quotaAdjustRecordRepository.save(record);
            }
        }


    }

    private void saveCreditFlow(String creditId,String applCde,String custId){
        CYBKCreditFlow creditFlow = cybKCreditFlowRepository.findByCreditId(creditId).orElse(new CYBKCreditFlow());
        creditFlow.setCreditId(creditId);
        creditFlow.setCreditNo(applCde);
        creditFlow.setCustId(custId);
        creditFlow.setFundingModel(FundingModel.ALONE);
        cybKCreditFlowRepository.save(creditFlow);
    }

    /**
     * 是否支持该资方渠道
     *
     * @param channel 资方渠道
     * @return 支持情况
     */
    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.CYBK == channel;
    }


}
