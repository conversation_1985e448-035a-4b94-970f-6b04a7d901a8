package com.jinghang.capital.core.banks.hxbk.enums;


import com.jinghang.capital.core.enums.FileType;

import java.util.Arrays;

/**
 * 影像类型
 */
public enum HXBKFileType {
    /**
     * 身份证正面（人像）
     */
    ID_FACE("1", "身份证正面（人像）", "idCardFront", FileType.ID_HEAD),

    /**
     * 身份证反面（国徽）
     */
    ID_NATION("2", "身份证反面（国徽）", "idCardBack", FileType.ID_NATION),

    /**
     * 授信环节人脸照
     */
    CREDIT_FACE_REC("3", "授信环节人脸照", "faceRecognition", FileType.ID_FACE),

    /**
     * 委托扣款授权书
     */
    WITHDRAW_PROTOCOL("11", "委托扣款授权书", "withdrawProtocal", FileType.ENTRUSTED_DEDUCTION_LETTER),

    /**
     * 综合授权书
     */
    SYNTHESIS_AUTHORIZATION("21", "综合授权书", "credit-mix-protocol", FileType.SYNTHESIS_AUTHORIZATION),

    /**
     * 非在校学生承诺函
     */
    NON_STUDENTS_DECLARE("74", "非在校学生承诺函", "non-students-declaration", FileType.PROMISE_NOT_STUDENT),

    /**
     * 个人放款用途承诺书
     */
    DEBET_PURPOSE_DECLARE("75", "个人放款用途承诺书", "debet-purpose-declaration", FileType.PERSONAL_LOAN_USE_COMMITMENT),

    /**
     * 数字证书授权使用书
     */
    DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER("29", "数字证书授权使用书", "digitalCertiface", FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER),


    /**
     * 个人借款合同
     */
    LOAN_CONTRACT("4", "个人借款合同", "huxiaoContract", FileType.LOAN_CONTRACT),

    /**
     * 征信授权书
     */
    PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT("6", "征信授权书", "credit-authorization", FileType.PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT),

    /**
     * 仲裁协议
     */
    ARBITRATION_AGREEMENT("11", "仲裁协议", "arbitration-agreement", FileType.ARBITRATION_AGREEMENT),

    /**
     * 担保合同
     */
    PARTNER_CONTRACT("3", "担保合同", "guaranteeContract", FileType.ENTRUSTED_GUARANTEE_CONTRACT),

    /**
     * 结清证明
     */
    DISCHARGE_CERT("11", "结清证明", "dischargeCertiface", FileType.CREDIT_SETTLE_VOUCHER_FILE);

    /**
     * 交易码
     */
    private final String code;

    /**
     * 交易名称
     */
    private final String desc;

    /**
     * 文件名
     */
    private final String fileName;

    private final FileType fileType;


    HXBKFileType( String code, String desc, String fileName, FileType fileType ) {
        this.code = code;
        this.desc = desc;
        this.fileName = fileName;
        this.fileType = fileType;
    }

    /**
     * 获取湖消文件类型
     *
     * @param fileType core文件类型
     */
    public static HXBKFileType getEnumByFileType( FileType fileType ) {
        return Arrays.stream(values()).filter(l -> fileType.equals(l.getFileType())).findFirst().orElse(null);
    }

    public FileType getFileType() {
        return fileType;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getFileName() {
        return fileName;
    }
}
